$(document).ready(function() {
    // Stocke les spans déjà traités pour éviter les doublons
    let processedSpans = [];
    
    // Fonction pour calculer la somme des valeurs des spans
    const calculateSum = () => {
        let sum = 0;
        // Sélectionne tous les spans avec l'attribut aria-valuenow
        const spans = $('span[aria-valuenow]');
        
        // Parcourt chaque span et ajoute sa valeur à la somme
        spans.each(function() {
            const currentValue = parseInt($(this).attr('aria-valuenow'), 10);
            if (!isNaN(currentValue)) {
                sum += currentValue;
            }
        });
        
        console.log(`Somme totale des valeurs : ${sum}`);
  		
       const label = $('label[for="sgE-90812264-20-214-10422"]');
  		$('#sgE-90812264-20-214').val(sum);
        if (label.length > 0) {
           
            label.html(` : <span class="sum-value">${sum}</span>`);
        }
        return sum;
    };
    
    // Fonction pour vérifier les spans
    const checkSpans = () => {
        try {
            // Sélectionne tous les spans avec l'attribut aria-valuenow
            const spans = $('span[aria-valuenow]');
            
            if (spans.length === 0) {
                console.log('Aucun span avec aria-valuenow trouvé');
                return;
            }
            
            // Parcourt chaque span et affiche sa valeur
            spans.each(function() {
                const spanId = $(this).attr('aria-labelledby'); // Identifiant unique basé sur aria-labelledby
                const currentValue = $(this).attr('aria-valuenow'); // Récupère la valeur de l'attribut
                
                // Vérifie si cet élément a déjà été traité
                if (!processedSpans.includes(spanId)) {
                    console.log(`Nouvel élément détecté : ${spanId}, Valeur actuelle = ${currentValue}`);
                    processedSpans.push(spanId); // Ajoute l'élément aux spans traités
                } else {
                    console.log(`Mise à jour élément : ${spanId}, Valeur actuelle = ${currentValue}`);
                }
            });
            
            // Calcule et affiche la somme après avoir traité tous les spans
            calculateSum();
        } catch (error) {
            console.error('Une erreur est survenue lors de la vérification des spans :', error);
        }
    };
    
    // Configuration de l'observateur de mutations
    const observerConfig = {
        attributes: true, // Observer les changements d'attributs
        subtree: true,   // Observer tous les descendants
        attributeFilter: ['aria-valuenow'] // Filtrer uniquement les changements sur aria-valuenow
    };
    
    // Callback pour l'observateur de mutations
    const observerCallback = (mutationsList) => {
        for (const mutation of mutationsList) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'aria-valuenow') {
                checkSpans(); // Vérifie les spans lorsqu'un changement est détecté
            }
        }
    };
    
    // Création de l'observateur de mutations
    const observer = new MutationObserver(observerCallback);
    
    // Démarrage de l'observation sur le document
    observer.observe(document.body, observerConfig);
    
    // Vérification initiale
    checkSpans();
});
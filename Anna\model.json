POST APPEL: {
    "attributes": {
        "CUSTOMER_ID": "{{customer_id}}",
        "EXPERIENCE_ID_PA": "{{experience_id}}",
        "DATE_CREATION": "{{date_de_creation}}"
        },
    "listIds": [
        5
    ],
    "updateEnabled": true,
    "email": "<EMAIL>"
}

POST FORMULAIRE: {
    "attributes": {
        "CUSTOMER_ID": "{{customer_id}}",
        "EXPERIENCE_ID_PF": "{{experience_id}}",
        "DATE_CREATION": "{{date_de_creation}}"
        },
    "listIds": [
        6
    ],
    "updateEnabled": true,
    "email": "<EMAIL>"
}


POST RAPPEL: {
    "attributes": {
        "CUSTOMER_ID": "{{customer_id}}",
        "EXPERIENCE_ID_PR": "{{experience_id}}",
        "DATE_CREATION": "{{date_de_creation}}"
        },
    "listIds": [
        7
    ],
    "updateEnabled": true,
    "email": "<EMAIL>"
}

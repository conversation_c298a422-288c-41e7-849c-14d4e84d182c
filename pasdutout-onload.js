// Script pour vérifier et modifier les éléments avec classes spécifiques
// Gère les cas: "pasdutout", "nondutout", "attention"
// Co<PERSON><PERSON>-collez ce code dans la console du navigateur

// Configuration des textes pour chaque cas
const textConfig = {
    pasdutout: {
        leftText: "Pas du tout satisfait(e) 😟",
        rightText: "Très satisfait(e) 😍"
    },
    nondutout: {
        leftText: "Non, pas du tout 😟",
        rightText: "Oui, tout à fait 😍"
    },
    attention: {
        leftText: "Pas attentionnées 😟",
        rightText: "Très attentionnées 😍"
    }
};

function checkAndUpdateElements() {
    console.log('🔍 === VÉRIFICATION ET MODIFICATION DES ÉLÉMENTS ===');

    // Chercher tous les éléments avec les classes cibles
    const allTargetElements = [
        ...document.querySelectorAll('.pasdutout'),
        ...document.querySelectorAll('.nondutout'),
        ...document.querySelectorAll('.attention')
    ];

    console.log(`📊 Nombre total d'éléments trouvés: ${allTargetElements.length}`);

    if (allTargetElements.length === 0) {
        console.log('❌ Aucun élément trouvé avec les classes: pasdutout, nondutout, attention');
        console.log('💡 Vérifications suggérées:');
        console.log('   - La page est-elle complètement chargée ?');
        console.log('   - Les classes existent-elles dans le HTML ?');
        console.log('   - Y a-t-il une faute de frappe ?');
        return [];
    }

    let updatedCount = 0;

    // Traitement de chaque élément
    allTargetElements.forEach((element, index) => {
        console.log(`\n🎯 === ÉLÉMENT ${index + 1} ===`);

        // Déterminer le type d'élément
        let elementType = null;
        if (element.classList.contains('pasdutout')) {
            elementType = 'pasdutout';
        } else if (element.classList.contains('nondutout')) {
            elementType = 'nondutout';
        } else if (element.classList.contains('attention')) {
            elementType = 'attention';
        }

        console.log(`Type: ${elementType}`);
        console.log(`Tag: ${element.tagName}`);
        console.log(`Classes: "${element.className}"`);
        console.log(`ID: ${element.id || '(aucun)'}`);

        // Chercher l'élément header-band
        const headerBand = element.querySelector('.header-band');
        if (!headerBand) {
            console.log('❌ Aucun élément .header-band trouvé dans cet élément');
            return;
        }

        console.log('✅ Élément .header-band trouvé');

        // Chercher les éléments p avec les classes spécifiques
        const leftGreenText = headerBand.querySelector('p.left-green-text');
        const rightRedText = headerBand.querySelector('p.right-red-text');

        if (!leftGreenText || !rightRedText) {
            console.log('❌ Éléments p.left-green-text ou p.right-red-text manquants');
            console.log(`   left-green-text: ${leftGreenText ? 'trouvé' : 'manquant'}`);
            console.log(`   right-red-text: ${rightRedText ? 'trouvé' : 'manquant'}`);
            return;
        }

        console.log('✅ Éléments de texte trouvés');

        // Afficher les textes actuels
        console.log(`Texte actuel gauche: "${leftGreenText.textContent}"`);
        console.log(`Texte actuel droite: "${rightRedText.textContent}"`);

        // Mettre à jour les textes selon le type
        if (elementType && textConfig[elementType]) {
            const config = textConfig[elementType];

            leftGreenText.textContent = config.leftText;
            rightRedText.textContent = config.rightText;

            console.log(`✅ Textes mis à jour pour type "${elementType}":`);
            console.log(`   Nouveau texte gauche: "${config.leftText}"`);
            console.log(`   Nouveau texte droite: "${config.rightText}"`);

            updatedCount++;
        } else {
            console.log('❌ Type d\'élément non reconnu ou configuration manquante');
        }
    });

    console.log(`\n✅ === TRAITEMENT TERMINÉ ===`);
    console.log(`📊 Résumé: ${updatedCount} éléments mis à jour sur ${allTargetElements.length} trouvés`);

    return allTargetElements;
}

// Version simplifiée
function simpleCheck() {
    const elements = document.querySelectorAll('.pasdutout');
    console.log(`🔍 Éléments .pasdutout: ${elements.length}`);

    elements.forEach((el, i) => {
        console.log(`${i+1}. ${el.tagName} - Classes: "${el.className}"`);
        console.log(`   Élément:`, el);
    });

    return elements;
}

// Version avec highlighting
function highlightElements() {
    const elements = document.querySelectorAll('.pasdutout');

    elements.forEach((element, index) => {
        // Style de surbrillance
        element.style.border = '3px solid red';
        element.style.backgroundColor = 'yellow';
        element.style.position = 'relative';

        // Ajouter un label
        const label = document.createElement('div');
        label.textContent = `PASDUTOUT ${index + 1}`;
        label.style.position = 'absolute';
        label.style.top = '-25px';
        label.style.left = '0';
        label.style.backgroundColor = 'red';
        label.style.color = 'white';
        label.style.padding = '2px 8px';
        label.style.fontSize = '12px';
        label.style.fontWeight = 'bold';
        label.style.zIndex = '9999';
        label.className = 'pasdutout-highlight-label';

        element.appendChild(label);
    });

    console.log(`✨ ${elements.length} éléments mis en surbrillance`);

    // Fonction de nettoyage
    window.clearPasdutoutHighlight = function() {
        const elements = document.querySelectorAll('.pasdutout');
        elements.forEach(element => {
            element.style.border = '';
            element.style.backgroundColor = '';
            const labels = element.querySelectorAll('.pasdutout-highlight-label');
            labels.forEach(label => label.remove());
        });
        console.log('🧹 Surbrillance supprimée');
    };

    return elements;
}

// Fonction principale avec window.onload
function initWithOnload() {
    console.log('🚀 Initialisation avec window.onload...');

    if (document.readyState === 'complete') {
        console.log('✅ Page déjà complètement chargée');
        checkAndUpdateElements();
    } else {
        console.log('⏳ Attente du chargement complet de la page...');
        window.addEventListener('load', function() {
            console.log('✅ Page complètement chargée! Exécution...');
            checkAndUpdateElements();
        });
    }
}

// Fonction pour mise à jour manuelle
function updateTexts() {
    console.log('🔄 Mise à jour manuelle des textes...');
    checkAndUpdateElements();
}

// Instructions et exécution automatique
console.log('📋 Script de modification des textes avec window.onload chargé!');
console.log('💡 Fonctions disponibles:');
console.log('   - checkAndUpdateElements() : Vérification et mise à jour complète');
console.log('   - updateTexts() : Mise à jour manuelle');
console.log('   - simpleCheck() : Analyse simple sans modification');
console.log('   - highlightElements() : Surbrillance visuelle');
console.log('   - clearPasdutoutHighlight() : Supprimer surbrillance');
console.log('   - initWithOnload() : Relancer avec window.onload');
console.log('');
console.log('🎯 Configuration des textes:');
Object.keys(textConfig).forEach(type => {
    console.log(`   ${type}:`);
    console.log(`     Gauche: "${textConfig[type].leftText}"`);
    console.log(`     Droite: "${textConfig[type].rightText}"`);
});
console.log('');

// Exécution automatique
initWithOnload();

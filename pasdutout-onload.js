// Script pour vérifier les éléments .pasdutout avec window.onload
// Copiez-collez ce code dans la console du navigateur

function checkPasdutoutElements() {
    console.log('🔍 === VÉRIFICATION ÉLÉMENTS PASDUTOUT ===');
    
    const elements = document.querySelectorAll('.pasdutout');
    console.log(`📊 Nombre d'éléments trouvés: ${elements.length}`);
    
    if (elements.length === 0) {
        console.log('❌ Aucun élément avec la classe "pasdutout" trouvé');
        console.log('💡 Vérifications suggérées:');
        console.log('   - La page est-elle complètement chargée ?');
        console.log('   - La classe "pasdutout" existe-t-elle dans le HTML ?');
        console.log('   - Y a-t-il une faute de frappe ?');
        return [];
    }
    
    // Affichage détaillé de chaque élément
    elements.forEach((element, index) => {
        console.log(`\n🎯 === ÉLÉMENT ${index + 1} ===`);
        
        // Informations de base
        console.log(`Tag: ${element.tagName}`);
        console.log(`Classes: "${element.className}"`);
        console.log(`ID: ${element.id || '(aucun)'}`);
        
        // Contenu texte
        try {
            const text = (element.textContent || element.innerText || '').trim();
            if (text) {
                console.log(`Texte: "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"`);
            } else {
                console.log(`Texte: (vide ou non accessible)`);
            }
        } catch (e) {
            console.log(`Texte: (erreur - ${e.message})`);
        }
        
        // Attributs principaux
        try {
            const attrs = [];
            for (let i = 0; i < element.attributes.length; i++) {
                const attr = element.attributes[i];
                attrs.push(`${attr.name}="${attr.value.substring(0, 50)}${attr.value.length > 50 ? '...' : ''}"`);
            }
            if (attrs.length > 0) {
                console.log(`Attributs: ${attrs.join(', ')}`);
            }
        } catch (e) {
            console.log(`Attributs: (erreur - ${e.message})`);
        }
        
        // HTML externe (début)
        try {
            const html = element.outerHTML;
            console.log(`HTML: ${html.substring(0, 200)}${html.length > 200 ? '...' : ''}`);
        } catch (e) {
            console.log(`HTML: (erreur - ${e.message})`);
        }
        
        // Parent
        try {
            const parent = element.parentElement;
            if (parent) {
                console.log(`Parent: ${parent.tagName}${parent.className ? '.' + parent.className.split(' ').join('.') : ''}`);
            }
        } catch (e) {
            console.log(`Parent: (erreur - ${e.message})`);
        }
        
        // Référence à l'élément pour inspection
        console.log(`Élément DOM:`, element);
    });
    
    console.log('\n✅ === VÉRIFICATION TERMINÉE ===');
    return elements;
}

// Version simplifiée
function simpleCheck() {
    const elements = document.querySelectorAll('.pasdutout');
    console.log(`🔍 Éléments .pasdutout: ${elements.length}`);
    
    elements.forEach((el, i) => {
        console.log(`${i+1}. ${el.tagName} - Classes: "${el.className}"`);
        console.log(`   Élément:`, el);
    });
    
    return elements;
}

// Version avec highlighting
function highlightElements() {
    const elements = document.querySelectorAll('.pasdutout');
    
    elements.forEach((element, index) => {
        // Style de surbrillance
        element.style.border = '3px solid red';
        element.style.backgroundColor = 'yellow';
        element.style.position = 'relative';
        
        // Ajouter un label
        const label = document.createElement('div');
        label.textContent = `PASDUTOUT ${index + 1}`;
        label.style.position = 'absolute';
        label.style.top = '-25px';
        label.style.left = '0';
        label.style.backgroundColor = 'red';
        label.style.color = 'white';
        label.style.padding = '2px 8px';
        label.style.fontSize = '12px';
        label.style.fontWeight = 'bold';
        label.style.zIndex = '9999';
        label.className = 'pasdutout-highlight-label';
        
        element.appendChild(label);
    });
    
    console.log(`✨ ${elements.length} éléments mis en surbrillance`);
    
    // Fonction de nettoyage
    window.clearPasdutoutHighlight = function() {
        const elements = document.querySelectorAll('.pasdutout');
        elements.forEach(element => {
            element.style.border = '';
            element.style.backgroundColor = '';
            const labels = element.querySelectorAll('.pasdutout-highlight-label');
            labels.forEach(label => label.remove());
        });
        console.log('🧹 Surbrillance supprimée');
    };
    
    return elements;
}

// Fonction principale avec window.onload
function initWithOnload() {
    console.log('🚀 Initialisation avec window.onload...');
    
    if (document.readyState === 'complete') {
        console.log('✅ Page déjà complètement chargée');
        checkPasdutoutElements();
    } else {
        console.log('⏳ Attente du chargement complet de la page...');
        window.addEventListener('load', function() {
            console.log('✅ Page complètement chargée! Exécution...');
            checkPasdutoutElements();
        });
    }
}

// Instructions et exécution automatique
console.log('📋 Script de vérification pasdutout avec window.onload chargé!');
console.log('💡 Fonctions disponibles:');
console.log('   - checkPasdutoutElements() : Analyse complète');
console.log('   - simpleCheck() : Analyse simple');
console.log('   - highlightElements() : Surbrillance visuelle');
console.log('   - clearPasdutoutHighlight() : Supprimer surbrillance');
console.log('   - initWithOnload() : Relancer avec window.onload');
console.log('');

// Exécution automatique
initWithOnload();

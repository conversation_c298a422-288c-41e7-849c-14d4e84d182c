// Script pour vérifier et modifier les éléments avec classes spécifiques
// Gère les cas: "pasdutout", "nondutout", "attention"
// Co<PERSON><PERSON>-collez ce code dans la console du navigateur

// Configuration des textes pour chaque cas
const textConfig = {
    pasdutout: {
        leftText: "Pas du tout satisfait(e) 😟",
        rightText: "Très satisfait(e) 😍"
    },
    nondutout: {
        leftText: "Non, pas du tout 😟",
        rightText: "Oui, tout à fait 😍"
    },
    attention: {
        leftText: "Pas attentionnées 😟",
        rightText: "Très attentionnées 😍"
    }
};

function checkAndUpdateElements() {
    console.log('🔍 === VÉRIFICATION ET MODIFICATION DES ÉLÉMENTS ===');

    // Chercher tous les éléments avec les classes cibles
    const allTargetElements = [
        ...document.querySelectorAll('.pasdutout'),
        ...document.querySelectorAll('.nondutout'),
        ...document.querySelectorAll('.attention')
    ];

    console.log(`📊 Nombre total d'éléments trouvés: ${allTargetElements.length}`);

    if (allTargetElements.length === 0) {
        console.log('❌ Aucun élément trouvé avec les classes: pasdutout, nondutout, attention');
        console.log('💡 Vérifications suggérées:');
        console.log('   - La page est-elle complètement chargée ?');
        console.log('   - Les classes existent-elles dans le HTML ?');
        console.log('   - Y a-t-il une faute de frappe ?');
        return [];
    }

    let updatedCount = 0;

    // Traitement de chaque élément
    allTargetElements.forEach((element, index) => {
        console.log(`\n🎯 === ÉLÉMENT ${index + 1} ===`);

        // Déterminer le type d'élément
        let elementType = null;
        if (element.classList.contains('pasdutout')) {
            elementType = 'pasdutout';
        } else if (element.classList.contains('nondutout')) {
            elementType = 'nondutout';
        } else if (element.classList.contains('attention')) {
            elementType = 'attention';
        }

        console.log(`Type: ${elementType}`);
        console.log(`Tag: ${element.tagName}`);
        console.log(`Classes: "${element.className}"`);
        console.log(`ID: ${element.id || '(aucun)'}`);

        // Chercher l'élément header-band
        const headerBand = element.querySelector('.header-band');
        if (!headerBand) {
            console.log('❌ Aucun élément .header-band trouvé dans cet élément');
            return;
        }

        console.log('✅ Élément .header-band trouvé');

        // Chercher les éléments p avec les classes spécifiques
        const leftGreenText = headerBand.querySelector('p.left-green-text');
        const rightRedText = headerBand.querySelector('p.right-red-text');

        if (!leftGreenText || !rightRedText) {
            console.log('❌ Éléments p.left-green-text ou p.right-red-text manquants');
            console.log(`   left-green-text: ${leftGreenText ? 'trouvé' : 'manquant'}`);
            console.log(`   right-red-text: ${rightRedText ? 'trouvé' : 'manquant'}`);
            return;
        }

        console.log('✅ Éléments de texte trouvés');

        // Afficher les textes actuels
        console.log(`Texte actuel gauche: "${leftGreenText.textContent}"`);
        console.log(`Texte actuel droite: "${rightRedText.textContent}"`);

        // Mettre à jour les textes selon le type
        if (elementType && textConfig[elementType]) {
            const config = textConfig[elementType];

            // Sauvegarder les anciens textes
            const oldLeftText = leftGreenText.textContent;
            const oldRightText = rightRedText.textContent;

            // Essayer plusieurs méthodes de modification
            try {
                // Méthode 1: textContent
                leftGreenText.textContent = config.leftText;
                rightRedText.textContent = config.rightText;

                // Méthode 2: innerHTML (au cas où)
                leftGreenText.innerHTML = config.leftText;
                rightRedText.innerHTML = config.rightText;

                // Méthode 3: innerText
                leftGreenText.innerText = config.leftText;
                rightRedText.innerText = config.rightText;

                // Forcer un repaint visuel
                leftGreenText.style.display = 'none';
                rightRedText.style.display = 'none';
                setTimeout(() => {
                    leftGreenText.style.display = '';
                    rightRedText.style.display = '';
                }, 10);

                // Ajouter une couleur temporaire pour voir le changement
                leftGreenText.style.backgroundColor = 'yellow';
                rightRedText.style.backgroundColor = 'yellow';
                setTimeout(() => {
                    leftGreenText.style.backgroundColor = '';
                    rightRedText.style.backgroundColor = '';
                }, 2000);

                console.log(`✅ Textes mis à jour pour type "${elementType}":`);
                console.log(`   Ancien texte gauche: "${oldLeftText}"`);
                console.log(`   Nouveau texte gauche: "${config.leftText}"`);
                console.log(`   Ancien texte droite: "${oldRightText}"`);
                console.log(`   Nouveau texte droite: "${config.rightText}"`);

                // Vérification après modification
                setTimeout(() => {
                    console.log(`🔍 Vérification après 500ms:`);
                    console.log(`   Gauche actuel: "${leftGreenText.textContent}"`);
                    console.log(`   Droite actuel: "${rightRedText.textContent}"`);
                }, 500);

                updatedCount++;

            } catch (error) {
                console.log(`❌ Erreur lors de la modification: ${error.message}`);
            }
        } else {
            console.log('❌ Type d\'élément non reconnu ou configuration manquante');
        }
    });

    console.log(`\n✅ === TRAITEMENT TERMINÉ ===`);
    console.log(`📊 Résumé: ${updatedCount} éléments mis à jour sur ${allTargetElements.length} trouvés`);

    return allTargetElements;
}


// Fonction principale avec délai pour s'exécuter après les autres scripts
function initWithDelay() {
    console.log('🚀 Initialisation avec délai pour attendre les autres scripts...');

    function executeAfterDelay() {
        console.log('✅ Exécution après délai...');
        checkAndUpdateElements();

        // Exécuter encore une fois après 2 secondes au cas où
        setTimeout(() => {
            console.log('🔄 Exécution de sécurité après 2 secondes...');
            checkAndUpdateElements();
        }, 2000);
    }

    if (document.readyState === 'complete') {
        console.log('✅ Page déjà complètement chargée');
        // Attendre 500ms pour laisser les autres scripts s'exécuter
        setTimeout(executeAfterDelay, 500);
    } else {
        console.log('⏳ Attente du chargement complet de la page...');
        window.addEventListener('load', function() {
            console.log('✅ Page complètement chargée! Attente de 500ms...');
            // Attendre 500ms après le load pour laisser les autres scripts s'exécuter
            setTimeout(executeAfterDelay, 500);
        });
    }
}

// Fonction avec window.onload classique
function initWithOnload() {
    console.log('🚀 Initialisation avec window.onload...');

    if (document.readyState === 'complete') {
        console.log('✅ Page déjà complètement chargée');
        checkAndUpdateElements();
    } else {
        console.log('⏳ Attente du chargement complet de la page...');
        window.addEventListener('load', function() {
            console.log('✅ Page complètement chargée! Exécution...');
            checkAndUpdateElements();
        });
    }
}

// Fonction pour mise à jour manuelle
function updateTexts() {
    console.log('🔄 Mise à jour manuelle des textes...');
    checkAndUpdateElements();
}

// Fonction de debug pour identifier les problèmes de sélecteurs
function debugSelectors() {
    console.log('🔍 === DEBUG SÉLECTEURS ===');

    // Chercher tous les éléments possibles
    const pasdutout = document.querySelectorAll('.pasdutout');
    const nondutout = document.querySelectorAll('.nondutout');
    const attention = document.querySelectorAll('.attention');

    console.log(`Éléments .pasdutout: ${pasdutout.length}`);
    console.log(`Éléments .nondutout: ${nondutout.length}`);
    console.log(`Éléments .attention: ${attention.length}`);

    // Chercher tous les header-band
    const allHeaderBands = document.querySelectorAll('.header-band');
    console.log(`Éléments .header-band: ${allHeaderBands.length}`);

    // Chercher tous les left-green-text et right-red-text
    const allLeftGreen = document.querySelectorAll('.left-green-text');
    const allRightRed = document.querySelectorAll('.right-red-text');
    console.log(`Éléments .left-green-text: ${allLeftGreen.length}`);
    console.log(`Éléments .right-red-text: ${allRightRed.length}`);

    // Analyser chaque header-band
    allHeaderBands.forEach((headerBand, index) => {
        console.log(`\n📋 Header-band ${index + 1}:`);
        console.log(`   Classes: "${headerBand.className}"`);

        const leftTexts = headerBand.querySelectorAll('p.left-green-text');
        const rightTexts = headerBand.querySelectorAll('p.right-red-text');

        console.log(`   p.left-green-text trouvés: ${leftTexts.length}`);
        console.log(`   p.right-red-text trouvés: ${rightTexts.length}`);

        leftTexts.forEach((lt, i) => {
            console.log(`     Left ${i+1}: "${lt.textContent}"`);
        });

        rightTexts.forEach((rt, i) => {
            console.log(`     Right ${i+1}: "${rt.textContent}"`);
        });

        // Lister tous les p dans ce header-band
        const allPs = headerBand.querySelectorAll('p');
        console.log(`   Tous les p (${allPs.length}):`);
        allPs.forEach((p, i) => {
            console.log(`     P${i+1}: Classes="${p.className}" Texte="${p.textContent.trim()}"`);
        });
    });

    console.log('\n✅ === DEBUG SÉLECTEURS TERMINÉ ===');
}

// Fonction pour forcer la mise à jour avec des méthodes alternatives
function forceUpdate() {
    console.log('💪 === MISE À JOUR FORCÉE ===');

    const textConfig = {
        pasdutout: {
            leftText: "Pas du tout satisfait(e) 😟",
            rightText: "Très satisfait(e) 😍"
        },
        nondutout: {
            leftText: "Non, pas du tout 😟",
            rightText: "Oui, tout à fait 😍"
        },
        attention: {
            leftText: "Pas attentionnées 😟",
            rightText: "Très attentionnées 😍"
        }
    };

    // Essayer de modifier TOUS les éléments left-green-text et right-red-text
    const allLeftGreen = document.querySelectorAll('.left-green-text');
    const allRightRed = document.querySelectorAll('.right-red-text');

    console.log(`Modification forcée de ${allLeftGreen.length} éléments left-green-text`);
    console.log(`Modification forcée de ${allRightRed.length} éléments right-red-text`);

    // Pour chaque paire, essayer de déterminer le type depuis le parent
    allLeftGreen.forEach((leftEl, index) => {
        const rightEl = allRightRed[index];
        if (!rightEl) return;

        // Chercher le parent avec une classe de type
        let parent = leftEl.parentElement;
        let elementType = null;

        while (parent && !elementType) {
            if (parent.classList.contains('pasdutout')) elementType = 'pasdutout';
            else if (parent.classList.contains('nondutout')) elementType = 'nondutout';
            else if (parent.classList.contains('attention')) elementType = 'attention';
            parent = parent.parentElement;
        }

        console.log(`Paire ${index + 1}: Type détecté = ${elementType}`);

        if (elementType && textConfig[elementType]) {
            const config = textConfig[elementType];

            // Modification avec toutes les méthodes
            leftEl.textContent = config.leftText;
            leftEl.innerHTML = config.leftText;
            leftEl.innerText = config.leftText;

            rightEl.textContent = config.rightText;
            rightEl.innerHTML = config.rightText;
            rightEl.innerText = config.rightText;

            // Effet visuel pour confirmer
            leftEl.style.backgroundColor = 'lightgreen';
            rightEl.style.backgroundColor = 'lightcoral';

            setTimeout(() => {
                leftEl.style.backgroundColor = '';
                rightEl.style.backgroundColor = '';
            }, 3000);

            console.log(`✅ Paire ${index + 1} mise à jour (${elementType})`);
        }
    });

    console.log('\n✅ === MISE À JOUR FORCÉE TERMINÉE ===');
}

// Fonction pour surveiller les changements DOM et réagir automatiquement
function startDOMObserver() {
    console.log('👁️ Démarrage de l\'observateur DOM...');

    const observer = new MutationObserver(function(mutations) {
        let shouldUpdate = false;

        mutations.forEach(function(mutation) {
            // Vérifier si des éléments .header-band ont été ajoutés
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        if (node.classList && node.classList.contains('header-band')) {
                            console.log('🔍 Nouvel élément .header-band détecté!');
                            shouldUpdate = true;
                        }
                        // Vérifier aussi dans les enfants
                        if (node.querySelectorAll && node.querySelectorAll('.header-band').length > 0) {
                            console.log('🔍 Nouveaux éléments .header-band détectés dans les enfants!');
                            shouldUpdate = true;
                        }
                    }
                });
            }
        });

        if (shouldUpdate) {
            console.log('🔄 Mise à jour automatique déclenchée par l\'observateur DOM...');
            // Attendre un peu que l'autre script finisse
            setTimeout(() => {
                checkAndUpdateElements();
            }, 100);
        }
    });

    // Observer tout le document
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    console.log('✅ Observateur DOM actif');
    return observer;
}

// Instructions et exécution automatique
console.log('📋 Script de modification des textes avec window.onload chargé!');
console.log('💡 Fonctions disponibles:');
console.log('   - checkAndUpdateElements() : Vérification et mise à jour complète');
console.log('   - updateTexts() : Mise à jour manuelle');
console.log('   - debugSelectors() : Debug des sélecteurs CSS');
console.log('   - forceUpdate() : Mise à jour forcée alternative');
console.log('   - startDOMObserver() : Surveiller les changements DOM');
console.log('   - initWithOnload() : Relancer avec window.onload');
console.log('');
console.log('🎯 Configuration des textes:');
Object.keys(textConfig).forEach(type => {
    console.log(`   ${type}:`);
    console.log(`     Gauche: "${textConfig[type].leftText}"`);
    console.log(`     Droite: "${textConfig[type].rightText}"`);
});
console.log('');

// Fonction pour forcer la mise à jour en continu
function startContinuousUpdate() {
    console.log('🔄 Démarrage de la mise à jour continue...');

    // Exécuter immédiatement
    checkAndUpdateElements();

    // Puis toutes les 500ms
    setInterval(() => {
        checkAndUpdateElements();
    }, 500);

    console.log('✅ Mise à jour continue active (toutes les 500ms)');
}

// Exécution automatique avec délai
initWithDelay();

// Démarrer l'observateur DOM pour réagir aux changements
startDOMObserver();

// Démarrer la mise à jour continue
setTimeout(() => {
    startContinuousUpdate();
}, 1000);

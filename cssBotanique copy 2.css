* {
    outline: none !important;
}

.introTitle

/*text d'introduction*/
    {
    background-color: #00634C;
    color: #FFF !important;
    padding: 1.25rem;
    border-radius: 6px;
}

img.sg-header-image

/*image header*/
    {
    max-width: 100% !important;
    margin: auto;
    display: block;
}

td.sg-cell.sg-cell-1.sg-first-cell.sg-cell-label.sg-cell-left-label

/*text à gauche*/
    {
    color: #EE3C32;
    height: fit-content!important;
}

td.sg-cell.sg-cell-13.sg-last-cell.sg-cell-label.sg-cell-right-label,
td.sg-cell.sg-cell-14.sg-last-cell.sg-cell-label.sg-cell-right-label

/*text à droite*/
    {
    color: #7EBC35;
    height: fit-content!important;

}

.sg-max-characters {
    display: none;
}

.colorGreen {
    color: #00634C;
    font-weight: bold;
}

/*Mettre en blanc les fonds des questions tableau*/
.sg-type-table .sg-table .sg-even-row td,
.sg-type-table .sg-table .sg-even-row th {
    background-color: #fff !important;
}

/*Fin Mettre en blanc les fonds des questions tableau*/



/*CSS MAJ*/
.sg-survey-form {
    max-width: 900px;
    width: 90%
}





.sg-survey-form {
    width: 90%
}

.sg-rating-scale thead {
    display: none
}

tr.sg-rating-set .sg-first-cell,
tr.sg-rating-set .sg-last-cell {
    width: auto !important;
    position: absolute
}

tr.sg-rating-set .sg-first-cell {
    top: 10px;
    left: 0
}



tr.sg-rating-set {
    margin-top: 35px
}


/*input*/
/* Write your custom CSS here */


/* mettre le conteneur en display flex et aligner les td dedans */
.sg-rating-scale tbody .sg-rating-set,
.sg-table tbody .sg-odd-row {
    list-style-type: none;
    counter-reset: css-counter -1;
    display: flex !important;
    justify-content: center;
    align-items: center;
    gap: 1px
}

/* rupture du flex en mobile */

/* mettre les td en display block avec un hauteur de 60px */

/* mettre les chiffre dans les span */
.sg-rating-scale tbody .sg-rating-set span.sg-icon-target {
    counter-increment: css-counter 1;
}


/* c'est l'aspect des td en pseudo elements la couleur des textes en noir et les chiffres dedans*/
.sg-replace-icons .sg-rating-scale tbody input[type="radio"]+label span.sg-icon-target:before {
    width: 70px;
    height: 60px;
    content: counter(css-counter);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: inherit;
    color: black;
    cursor: pointer
}


/* couleur incrémenté par chiffre */



/* spécifique pour le non concerné */
.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(13) input[type="radio"]+label span.sg-icon-target::before {
    background: #FFF;
    margin-left: 5px;
    padding: 0 10px;
    content: 'Non concerné(e)';
    width: auto;
    border: 1px solid rgb(223, 223, 223);
    font-size: 13px;
}




.sg-replace-icons .sg-rating-scale tbody .sg-cell-2 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-2 input[type="radio"]:hover+label span.sg-icon-target:before {
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-3 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-3 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-4 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-4 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-5 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-5 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-6 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-6 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-7 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-7 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-8 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-8 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-9 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-9 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-10 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-10 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-11 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-11 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-12 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-12 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-13 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-13 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 1;
    box-shadow: rgb(0 49 52 / 25%) 0px 5px 16px;
    background-color: rgb(243, 245, 243);
    border-radius: 5px;
    outline: 2px dotted green !important;
    font-size: 13px !important;
    color: black !important;

}


/* pour l'option table */

.sg-table tbody .sg-even-row,
.sg-table tbody .sg-odd-row {
    list-style-type: none;
    counter-reset: css-counter -1;
    display: flex !important;
    gap: 1px;
    justify-content: start;
    align-items: center !important
}

.sg-table tbody .sg-even-row td,
.sg-table tbody .sg-odd-row td {
    padding: 0 !important;
    height: auto !important;
    margin-top: 10px;
}

.sg-table tbody .sg-even-row .sg-last-cell,
.sg-table tbody .sg-odd-row .sg-last-cell {
    padding: 0 !important;
    height: auto !important;
    margin-top: 0 !important;
}


.sg-table thead {
    display: none;
}

.sg-table tbody .sg-even-row .sg-first-cell,
.sg-table tbody .sg-odd-row .sg-first-cell {
    width: 261px;
}


/* mettre les chiffre dans les span */
.sg-table tbody .sg-odd-row td span.sg-icon-target,
.sg-table tbody .sg-even-row td span.sg-icon-target {
    counter-increment: css-counter 1;

}

/* c'est l'aspect des td en pseudo elements la couleur des textes en noir et les chiffres dedans*/
.sg-table tbody input[type="radio"]+label span.sg-icon-target:before {
    width: 40px;
    height: 40px;
    content: counter(css-counter);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: inherit;
    color: black;
    cursor: pointer;
    font-size: 12px;
    font-weight: 800;

}


/* couleur incrémenté par chiffre */
.sg-table tbody tr td:nth-child(2) input[type="radio"]+label span.sg-icon-target::before {
    background: #bd0000;
    border-radius: 10px 0 0 10px;
}

.sg-table tbody tr td:nth-child(3) input[type="radio"]+label span.sg-icon-target::before {
    background: #cf0000;
}

.sg-table tbody tr td:nth-child(4) input[type="radio"]+label span.sg-icon-target::before {
    background: #e62300;
}

.sg-table tbody tr td:nth-child(5) input[type="radio"]+label span.sg-icon-target::before {
    background: #e13600;
}

.sg-table tbody tr td:nth-child(6) input[type="radio"]+label span.sg-icon-target::before {
    background: #e55300;
}

.sg-table tbody tr td:nth-child(7) input[type="radio"]+label span.sg-icon-target::before {
    background: #e57c00;
}

.sg-table tbody tr td:nth-child(8) input[type="radio"]+label span.sg-icon-target::before {
    background: #e5a600;
}

.sg-table tbody tr td:nth-child(9) input[type="radio"]+label span.sg-icon-target::before {
    background: #8cb026;
}

.sg-table tbody tr td:nth-child(10) input[type="radio"]+label span.sg-icon-target::before {
    background: #7d9e22;
}

.sg-table tbody tr td:nth-child(11) input[type="radio"]+label span.sg-icon-target::before {
    background: #568528;
}

.sg-table tbody tr td:nth-child(12) input[type="radio"]+label span.sg-icon-target::before {
    background: #207716;
    border-radius: 0 10px 10px 0;
}

/* spécifique pour le non concerné */
.sg-table tbody tr td:nth-child(13) input[type="radio"]+label span.sg-icon-target::before {
    background: #FFF;
    margin-left: 5px;
    margin-top: 5px;
    padding: 0 5px;
    content: 'Non concerné(e)';
    width: auto;
    font-size: 13px;
    font-weight: normal !important;
}



/* hover pour les éléments de la table */
.sg-table tbody tr td:nth-child(2) input[type="radio"]:checked+label span.sg-icon-target::before,
.sg-table tbody tr td:nth-child(2) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);


}


.sg-table tbody tr td:nth-child(3) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(3) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(4) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(4) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(5) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(5) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(6) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(6) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(7) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(7) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(8) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(8) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(9) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(9) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(10) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(10) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(11) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(11) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(12) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(12) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(13) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(13) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 1;
    box-shadow: rgb(0 49 52 / 25%) 0px 5px 16px;
    background-color: rgb(243, 245, 243);
    border-radius: 5px;
    outline: 2px dotted green !important;
    font-size: 13px !important;
    color: black !important;
    transform: scale(1);

}

.sg-table tbody tr td input[type="radio"]:checked+label span.sg-icon-target:before {
    color: yellow;
    transform: scale(1.1);
}





.introTitle

/*text d'introduction*/
    {
    background-color: #00634C;
    color: #FFF !important;
    padding: 1.25rem;
    border-radius: 6px;
}

img.sg-header-image

/*image header*/
    {
    max-width: 100% !important;
    margin: auto;
    display: block;
}

td.sg-cell.sg-cell-1.sg-first-cell.sg-cell-label.sg-cell-left-label

/*text à gauche*/
    {
    color: #EE3C32;
}

td.sg-cell.sg-cell-13.sg-last-cell.sg-cell-label.sg-cell-right-label,
td.sg-cell.sg-cell-14.sg-last-cell.sg-cell-label.sg-cell-right-label

/*text à droite*/
    {
    color: #7EBC35;
}

.sg-max-characters {
    display: none;
}

.colorGreen {
    color: #00634C;
    font-weight: bold;
}

/*Mettre en blanc les fonds des questions tableau*/
.sg-type-table .sg-table .sg-even-row td,
.sg-type-table .sg-table .sg-even-row th {
    background-color: #fff !important;
}

/*Fin Mettre en blanc les fonds des questions tableau*/



/*CSS MAJ*/
.sg-survey-form {
    max-width: 900px;
    width: 90%
}



fieldset.sg-question.sg-type-radio,
fieldset.sg-type-table,
.sg-type-essay {
    width: 100%;
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    padding-top: 25px;
    padding-left: 15px;
    padding-right: 15px;
    border-radius: 15px;
    position: relative
}

fieldset.sg-question.sg-type-radio legend,
fieldset.sg-type-table legend {
    display: contents
}



.sg-rating-scale thead {
    display: none
}

tr.sg-rating-set .sg-first-cell,
tr.sg-rating-set .sg-last-cell {
    width: auto !important;
    position: absolute;
    height: fit-content !important;
}



tr.sg-rating-set .sg-last-cell {
    top: 10px;
    right: 0
}



.sg-rating-scale {

    position: relative
}

/*input*/
/* Write your custom CSS here */


/* mettre le conteneur en display flex et aligner les td dedans */

/* rupture du flex en mobile */

/* mettre les td en display block avec un hauteur de 60px */
.sg-rating-scale tbody .sg-rating-set td,
.sg-table tbody .sg-odd-row td {
    display: block !important;
    height: 60px !important;
    padding: 0 !important;
}

/* mettre les chiffre dans les span */

.sg-type-radio .sg-rating-scale-15 .sg-cell {
    width: auto
}

/* c'est l'aspect des td en pseudo elements la couleur des textes en noir et les chiffres dedans*/


/* couleur incrémenté par chiffre */
.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(2) input[type="radio"]+label span.sg-icon-target::before {
    background: #bd0000;
    border-radius: 10px 0 0 10px;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(3) input[type="radio"]+label span.sg-icon-target::before {
    background: #cf0000;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(4) input[type="radio"]+label span.sg-icon-target::before {
    background: #e62300;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(5) input[type="radio"]+label span.sg-icon-target::before {
    background: #e13600;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(6) input[type="radio"]+label span.sg-icon-target::before {
    background: #e55300;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(7) input[type="radio"]+label span.sg-icon-target::before {
    background: #e57c00;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(8) input[type="radio"]+label span.sg-icon-target::before {
    background: #e5a600;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(9) input[type="radio"]+label span.sg-icon-target::before {
    background: #8cb026;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(10) input[type="radio"]+label span.sg-icon-target::before {
    background: #7d9e22;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(11) input[type="radio"]+label span.sg-icon-target::before {
    background: #568528;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(12) input[type="radio"]+label span.sg-icon-target::before {
    background: #207716;
    border-radius: 0 10px 10px 0;
}

/* spécifique pour le non concerné */




.sg-replace-icons .sg-rating-scale tbody .sg-cell-2 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-2 input[type="radio"]:hover+label span.sg-icon-target:before {
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-3 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-3 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-4 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-4 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-5 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-5 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-6 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-6 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-7 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-7 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-8 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-8 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-9 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-9 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-10 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-10 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-11 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-11 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-12 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-12 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}

.sg-replace-icons .sg-rating-scale tbody .sg-cell-13 input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody .sg-cell-13 input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 1;
    box-shadow: rgb(0 49 52 / 25%) 0px 5px 16px;
    background-color: rgb(243, 245, 243);
    border-radius: 5px;
    outline: 2px dotted green !important;
    font-size: 13px !important;
    color: black !important;

}


/* pour l'option table */

.sg-table tbody .sg-even-row,
.sg-table tbody .sg-odd-row {
    list-style-type: none;
    counter-reset: css-counter -1;
    display: flex !important;
    gap: 1px;
    justify-content: start;
    align-items: center !important
}

.sg-table tbody .sg-even-row td,
.sg-table tbody .sg-odd-row td {
    padding: 0 !important;
    height: auto !important;
    margin-top: 10px;
}

.sg-table tbody .sg-even-row .sg-last-cell,
.sg-table tbody .sg-odd-row .sg-last-cell {
    padding: 0 !important;
    height: auto !important;
    margin-top: 0 !important;
}


.sg-table thead {
    display: none;
}

.sg-table tbody .sg-even-row .sg-first-cell,
.sg-table tbody .sg-odd-row .sg-first-cell {
    width: 261px;
}


/* mettre les chiffre dans les span */
.sg-table tbody .sg-odd-row td span.sg-icon-target,
.sg-table tbody .sg-even-row td span.sg-icon-target {
    counter-increment: css-counter 1;

}

/* c'est l'aspect des td en pseudo elements la couleur des textes en noir et les chiffres dedans*/
.sg-table tbody input[type="radio"]+label span.sg-icon-target:before {
    width: 40px;
    height: 40px;
    content: counter(css-counter);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: inherit;
    color: black;
    cursor: pointer;
    font-size: 12px;
    font-weight: 800;

}


/* couleur incrémenté par chiffre */
.sg-table tbody tr td:nth-child(2) input[type="radio"]+label span.sg-icon-target::before {
    background: #bd0000;
    border-radius: 10px 0 0 10px;
}

.sg-table tbody tr td:nth-child(3) input[type="radio"]+label span.sg-icon-target::before {
    background: #cf0000;
}

.sg-table tbody tr td:nth-child(4) input[type="radio"]+label span.sg-icon-target::before {
    background: #e62300;
}

.sg-table tbody tr td:nth-child(5) input[type="radio"]+label span.sg-icon-target::before {
    background: #e13600;
}

.sg-table tbody tr td:nth-child(6) input[type="radio"]+label span.sg-icon-target::before {
    background: #e55300;
}

.sg-table tbody tr td:nth-child(7) input[type="radio"]+label span.sg-icon-target::before {
    background: #e57c00;
}

.sg-table tbody tr td:nth-child(8) input[type="radio"]+label span.sg-icon-target::before {
    background: #e5a600;
}

.sg-table tbody tr td:nth-child(9) input[type="radio"]+label span.sg-icon-target::before {
    background: #8cb026;
}

.sg-table tbody tr td:nth-child(10) input[type="radio"]+label span.sg-icon-target::before {
    background: #7d9e22;
}

.sg-table tbody tr td:nth-child(11) input[type="radio"]+label span.sg-icon-target::before {
    background: #568528;
}

.sg-table tbody tr td:nth-child(12) input[type="radio"]+label span.sg-icon-target::before {
    background: #207716;
    border-radius: 0 10px 10px 0;
}

/* spécifique pour le non concerné */
.sg-table tbody tr td:nth-child(13) input[type="radio"]+label span.sg-icon-target::before {
    background: #FFF;
    margin-left: 5px;
    margin-top: 5px;
    padding: 0 5px;
    content: 'Non concerné(e)';
    width: auto;
    font-size: 13px;
    font-weight: normal !important;
}



/* hover pour les éléments de la table */
.sg-table tbody tr td:nth-child(2) input[type="radio"]:checked+label span.sg-icon-target::before,
.sg-table tbody tr td:nth-child(2) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);


}


.sg-table tbody tr td:nth-child(3) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(3) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(4) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(4) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(5) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(5) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(6) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(6) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(7) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(7) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(8) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(8) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(9) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(9) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(10) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(10) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(11) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(11) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(12) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(12) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);

}

.sg-table tbody tr td:nth-child(13) input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-table tbody tr td:nth-child(13) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 1;
    box-shadow: rgb(0 49 52 / 25%) 0px 5px 16px;
    background-color: rgb(243, 245, 243);
    border-radius: 5px;
    outline: 2px dotted green !important;
    font-size: 13px !important;
    color: black !important;
    transform: scale(1);

}

.sg-table tbody tr td input[type="radio"]:checked+label span.sg-icon-target:before {
    color: yellow;
    transform: scale(1.1);
}



    .sg-question-set {

        width: 90vw;
        margin: auto;
    }

    .sg-content {
        width: 100%;
        padding: 0;
    }

    .sg-replace-icons .sg-rating-scale tbody input[type="radio"]+label span.sg-icon-target:before {
        width: 8vw !important;
        height: 28px !important;
        font-size: 12px;
    }

    fieldset.sg-question.sg-type-radio,
    fieldset.sg-type-table,
    .sg-type-essay {
        width: 100%;
        box-shadow: none;
        padding-top: 0;
        padding-left: 0;
        padding-right: 0;
        border-radius: 0;
        position: relative;
    }

    .sg-question-options {
        padding: 0;
        margin: 0;
    }

    .sg-type-radio-likert .sg-question-options {
        overflow: hidden;
    }

    * {
        outline: none !important;
    }

    td.sg-cell.sg-cell-1.sg-first-cell.sg-cell-label.sg-cell-left-label

    /*text à gauche*/
        {
        height: fit-content !important;
    }

    td.sg-cell.sg-cell-13.sg-last-cell.sg-cell-label.sg-cell-right-label,
    td.sg-cell.sg-cell-14.sg-last-cell.sg-cell-label.sg-cell-right-label

    /*text à droite*/
        {
        height: fit-content !important;
    }


    /* non concerné questions */
    .nonConcerned .sg-rating-set {
        padding-bottom: 37px;
        padding-top: 37px;
        position: relative
    }

    .nonConcerned .sg-rating-scale .sg-cell-13 {
        position: absolute;
        top: 82px
    }




    .nonConcerned .sg-rating-scale .sg-cell-13 input[type="radio"]+label span.sg-icon-target::before {
        width: 100% !important;
        transform: translate(-15px, 10px);
    }

    tr.sg-rating-set .sg-cell-left-label,
    tr.sg-rating-set .sg-cell-right-label {
        width: auto !important;
        position: absolute;
        height: fit-content !important;
    }

    * {
        outline: none !important;
    }

    .introTitle

    /*text d'introduction*/
        {
        background-color: #00634C;
        color: #FFF !important;
        padding: 1.25rem;
        border-radius: 6px;
    }

    img.sg-header-image

    /*image header*/
        {
        max-width: 100% !important;
        margin: auto;
        display: block;
    }

    td.sg-cell.sg-cell-1.sg-first-cell.sg-cell-label.sg-cell-left-label

    /*text à gauche*/
        {
        color: #EE3C32;
    }

    td.sg-cell.sg-cell-13.sg-last-cell.sg-cell-label.sg-cell-right-label,
    td.sg-cell.sg-cell-14.sg-last-cell.sg-cell-label.sg-cell-right-label

    /*text à droite*/
        {
        color: #7EBC35;
    }

    .sg-max-characters {
        display: none;
    }

    .colorGreen {
        color: #00634C;
        font-weight: bold;
    }

    /*Mettre en blanc les fonds des questions tableau*/
    .sg-type-table .sg-table .sg-even-row td,
    .sg-type-table .sg-table .sg-even-row th {
        background-color: #fff !important;
    }

    /*Fin Mettre en blanc les fonds des questions tableau*/



    /*CSS MAJ*/
    .sg-survey-form {
        max-width: 900px;
        width: 90%
    }



    fieldset.sg-question.sg-type-radio,
    fieldset.sg-type-table,
    .sg-type-essay {
        width: 100%;
        box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
        padding-top: 25px;
        padding-left: 15px;
        padding-right: 15px;
        border-radius: 15px;
        position: relative
    }

    fieldset.sg-question.sg-type-radio legend,
    fieldset.sg-type-table legend {
        display: contents
    }

    .sg-survey-form {
        width: 90%
    }

    .sg-rating-scale thead {
        display: none
    }

    tr.sg-rating-set .sg-first-cell,
    tr.sg-rating-set .sg-last-cell {
        width: auto !important;
        position: absolute
    }

    tr.sg-rating-set .sg-first-cell {
        top: 10px;
        left: 0
    }

    tr.sg-rating-set .sg-last-cell {
        top: 10px;
        right: 0
    }

    tr.sg-rating-set {
        margin-top: 35px
    }

    .sg-rating-scale {

        position: relative
    }

    /*input*/
    /* Write your custom CSS here */


    /* mettre le conteneur en display flex et aligner les td dedans */
    .sg-rating-scale tbody .sg-rating-set,
    .sg-table tbody .sg-odd-row {
        list-style-type: none;
        counter-reset: css-counter -1;
        display: flex !important;
        justify-content: center;
        align-items: center;
        gap: 1px
    }

    /* rupture du flex en mobile */

    /* mettre les td en display block avec un hauteur de 60px */
    .sg-rating-scale tbody .sg-rating-set td,
    .sg-table tbody .sg-odd-row td {
        display: block !important;
        height: 60px !important;
        padding: 0 !important;
    }

    /* mettre les chiffre dans les span */
    .sg-rating-scale tbody .sg-rating-set span.sg-icon-target {
        counter-increment: css-counter 1;
    }


    .sg-type-radio .sg-rating-scale-15 .sg-cell {
        width: auto
    }

    /* c'est l'aspect des td en pseudo elements la couleur des textes en noir et les chiffres dedans*/
    .sg-replace-icons .sg-rating-scale tbody input[type="radio"]+label span.sg-icon-target:before {
        width: 70px;
        height: 60px;
        content: counter(css-counter);
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: inherit;
        color: black;
        cursor: pointer
    }


    /* couleur incrémenté par chiffre */
    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(2) input[type="radio"]+label span.sg-icon-target::before {
        background: #bd0000;
        border-radius: 10px 0 0 10px;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(3) input[type="radio"]+label span.sg-icon-target::before {
        background: #cf0000;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(4) input[type="radio"]+label span.sg-icon-target::before {
        background: #e62300;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(5) input[type="radio"]+label span.sg-icon-target::before {
        background: #e13600;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(6) input[type="radio"]+label span.sg-icon-target::before {
        background: #e55300;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(7) input[type="radio"]+label span.sg-icon-target::before {
        background: #e57c00;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(8) input[type="radio"]+label span.sg-icon-target::before {
        background: #e5a600;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(9) input[type="radio"]+label span.sg-icon-target::before {
        background: #8cb026;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(10) input[type="radio"]+label span.sg-icon-target::before {
        background: #7d9e22;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(11) input[type="radio"]+label span.sg-icon-target::before {
        background: #568528;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(12) input[type="radio"]+label span.sg-icon-target::before {
        background: #207716;
        border-radius: 0 10px 10px 0;
    }

    /* spécifique pour le non concerné */
    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(13) input[type="radio"]+label span.sg-icon-target::before {
        background: #FFF;
        margin-left: 5px;
        padding: 0 10px;
        content: 'Non concerné(e)';
        width: auto;
        border: 1px solid rgb(223, 223, 223);
        font-size: 13px;
    }




    .sg-replace-icons .sg-rating-scale tbody .sg-cell-2 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-2 input[type="radio"]:hover+label span.sg-icon-target:before {
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-3 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-3 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-4 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-4 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-5 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-5 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-6 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-6 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-7 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-7 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-8 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-8 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-9 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-9 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    

    

    
    


    /* pour l'option table */


   

    /* c'est l'aspect des td en pseudo elements la couleur des textes en noir et les chiffres dedans*/
    


    /* couleur incrémenté par chiffre */
    

    
    /* spécifique pour le non concerné */
  



    /* hover pour les éléments de la table */
    .sg-table tbody tr td:nth-child(2) input[type="radio"]:checked+label span.sg-icon-target::before,
    .sg-table tbody tr td:nth-child(2) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);


    }


    .sg-table tbody tr td:nth-child(3) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(3) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(4) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(4) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(5) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(5) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(6) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(6) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(7) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(7) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(8) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(8) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(9) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(9) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(10) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(10) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(11) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(11) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(12) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(12) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(13) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(13) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 1;
        box-shadow: rgb(0 49 52 / 25%) 0px 5px 16px;
        background-color: rgb(243, 245, 243);
        border-radius: 5px;
        outline: 2px dotted green !important;
        font-size: 13px !important;
        color: black !important;
        transform: scale(1);

    }

    .sg-table tbody tr td input[type="radio"]:checked+label span.sg-icon-target:before {
        color: yellow;
        transform: scale(1.1);
    }





    .introTitle

    /*text d'introduction*/
        {
        background-color: #00634C;
        color: #FFF !important;
        padding: 1.25rem;
        border-radius: 6px;
    }

    img.sg-header-image

    /*image header*/
        {
        max-width: 100% !important;
        margin: auto;
        display: block;
    }

    td.sg-cell.sg-cell-1.sg-first-cell.sg-cell-label.sg-cell-left-label

    /*text à gauche*/
        {
        color: #EE3C32;
    }

    td.sg-cell.sg-cell-13.sg-last-cell.sg-cell-label.sg-cell-right-label,
    td.sg-cell.sg-cell-14.sg-last-cell.sg-cell-label.sg-cell-right-label

    /*text à droite*/
        {
        color: #7EBC35;
    }

    .sg-max-characters {
        display: none;
    }

    .colorGreen {
        color: #00634C;
        font-weight: bold;
    }

    /*Mettre en blanc les fonds des questions tableau*/
    .sg-type-table .sg-table .sg-even-row td,
    .sg-type-table .sg-table .sg-even-row th {
        background-color: #fff !important;
    }

    /*Fin Mettre en blanc les fonds des questions tableau*/



    /*CSS MAJ*/
    .sg-survey-form {
        max-width: 900px;
        width: 90%
    }



    fieldset.sg-question.sg-type-radio,
    fieldset.sg-type-table,
    .sg-type-essay {
        width: 100%;
        box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
        padding-top: 25px;
        padding-left: 15px;
        padding-right: 15px;
        border-radius: 15px;
        position: relative
    }

    fieldset.sg-question.sg-type-radio legend,
    fieldset.sg-type-table legend {
        display: contents
    }

    .sg-survey-form {
        width: 90%
    }

    .sg-rating-scale thead {
        display: none
    }

    tr.sg-rating-set .sg-first-cell,
    tr.sg-rating-set .sg-last-cell {
        width: auto !important;
        position: absolute;
        height: fit-content !important;
    }


    tr.sg-rating-set .sg-first-cell {
        top: 10px;
        left: 0
    }

    tr.sg-rating-set .sg-last-cell {
        top: 10px;
        right: 0
    }

    tr.sg-rating-set {
        margin-top: 35px
    }

    .sg-rating-scale {

        position: relative
    }

    /*input*/
    /* Write your custom CSS here */


    /* mettre le conteneur en display flex et aligner les td dedans */
    .sg-rating-scale tbody .sg-rating-set,
    .sg-table tbody .sg-odd-row {
        list-style-type: none;
        counter-reset: css-counter -1;
        display: flex !important;
        justify-content: center;
        align-items: center;
        gap: 1px
    }

    /* rupture du flex en mobile */

    /* mettre les td en display block avec un hauteur de 60px */
    .sg-rating-scale tbody .sg-rating-set td,
    .sg-table tbody .sg-odd-row td {
        display: block !important;
        height: 60px !important;
        padding: 0 !important;
    }

    /* mettre les chiffre dans les span */
    .sg-rating-scale tbody .sg-rating-set span.sg-icon-target {
        counter-increment: css-counter 1;
    }


    .sg-type-radio .sg-rating-scale-15 .sg-cell {
        width: auto
    }

    /* c'est l'aspect des td en pseudo elements la couleur des textes en noir et les chiffres dedans*/
    .sg-replace-icons .sg-rating-scale tbody input[type="radio"]+label span.sg-icon-target:before {
        width: 70px;
        height: 60px;
        content: counter(css-counter);
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: inherit;
        color: black;
        cursor: pointer
    }


    /* couleur incrémenté par chiffre */
    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(2) input[type="radio"]+label span.sg-icon-target::before {
        background: #bd0000;
        border-radius: 10px 0 0 10px;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(3) input[type="radio"]+label span.sg-icon-target::before {
        background: #cf0000;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(4) input[type="radio"]+label span.sg-icon-target::before {
        background: #e62300;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(5) input[type="radio"]+label span.sg-icon-target::before {
        background: #e13600;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(6) input[type="radio"]+label span.sg-icon-target::before {
        background: #e55300;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(7) input[type="radio"]+label span.sg-icon-target::before {
        background: #e57c00;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(8) input[type="radio"]+label span.sg-icon-target::before {
        background: #e5a600;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(9) input[type="radio"]+label span.sg-icon-target::before {
        background: #8cb026;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(10) input[type="radio"]+label span.sg-icon-target::before {
        background: #7d9e22;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(11) input[type="radio"]+label span.sg-icon-target::before {
        background: #568528;
    }

    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(12) input[type="radio"]+label span.sg-icon-target::before {
        background: #207716;
        border-radius: 0 10px 10px 0;
    }

    /* spécifique pour le non concerné */
    .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(13) input[type="radio"]+label span.sg-icon-target::before {
        background: #FFF;
        margin-left: 5px;
        padding: 0 10px;
        content: 'Non concerné(e)';
        width: auto;
        border: 1px solid rgb(223, 223, 223);
        font-size: 13px;
    }




    .sg-replace-icons .sg-rating-scale tbody .sg-cell-2 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-2 input[type="radio"]:hover+label span.sg-icon-target:before {
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-3 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-3 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-4 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-4 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-5 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-5 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-6 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-6 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-7 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-7 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-8 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-8 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-9 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-9 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-10 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-10 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-11 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-11 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-12 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-12 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        transform: scale(1.1);
        color: yellow
    }

    .sg-replace-icons .sg-rating-scale tbody .sg-cell-13 input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-replace-icons .sg-rating-scale tbody .sg-cell-13 input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 1;
        box-shadow: rgb(0 49 52 / 25%) 0px 5px 16px;
        background-color: rgb(243, 245, 243);
        border-radius: 5px;
        outline: 2px dotted green !important;
        font-size: 13px !important;
        color: black !important;

    }


    /* pour l'option table */

    .sg-table tbody .sg-even-row,
    .sg-table tbody .sg-odd-row {
        list-style-type: none;
        counter-reset: css-counter -1;
        display: flex !important;
        gap: 1px;
        justify-content: start;
        align-items: center !important
    }

    .sg-table tbody .sg-even-row td,
    .sg-table tbody .sg-odd-row td {
        padding: 0 !important;
        height: auto !important;
        margin-top: 10px;
    }

    .sg-table tbody .sg-even-row .sg-last-cell,
    .sg-table tbody .sg-odd-row .sg-last-cell {
        padding: 0 !important;
        height: auto !important;
        margin-top: 0 !important;
    }


    .sg-table thead {
        display: none;
    }

    .sg-table tbody .sg-even-row .sg-first-cell,
    .sg-table tbody .sg-odd-row .sg-first-cell {
        width: 261px;
    }


    /* mettre les chiffre dans les span */
    .sg-table tbody .sg-odd-row td span.sg-icon-target,
    .sg-table tbody .sg-even-row td span.sg-icon-target {
        counter-increment: css-counter 1;

    }

    /* c'est l'aspect des td en pseudo elements la couleur des textes en noir et les chiffres dedans*/
    .sg-table tbody input[type="radio"]+label span.sg-icon-target:before {
        width: 40px;
        height: 40px;
        content: counter(css-counter);
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: inherit;
        color: black;
        cursor: pointer;
        font-size: 12px;
        font-weight: 800;

    }


    /* couleur incrémenté par chiffre */
    .sg-table tbody tr td:nth-child(2) input[type="radio"]+label span.sg-icon-target::before {
        background: #bd0000;
        border-radius: 10px 0 0 10px;
    }

    .sg-table tbody tr td:nth-child(3) input[type="radio"]+label span.sg-icon-target::before {
        background: #cf0000;
    }

    .sg-table tbody tr td:nth-child(4) input[type="radio"]+label span.sg-icon-target::before {
        background: #e62300;
    }

    .sg-table tbody tr td:nth-child(5) input[type="radio"]+label span.sg-icon-target::before {
        background: #e13600;
    }

    .sg-table tbody tr td:nth-child(6) input[type="radio"]+label span.sg-icon-target::before {
        background: #e55300;
    }

    .sg-table tbody tr td:nth-child(7) input[type="radio"]+label span.sg-icon-target::before {
        background: #e57c00;
    }

    .sg-table tbody tr td:nth-child(8) input[type="radio"]+label span.sg-icon-target::before {
        background: #e5a600;
    }

    .sg-table tbody tr td:nth-child(9) input[type="radio"]+label span.sg-icon-target::before {
        background: #8cb026;
    }

    .sg-table tbody tr td:nth-child(10) input[type="radio"]+label span.sg-icon-target::before {
        background: #7d9e22;
    }

    .sg-table tbody tr td:nth-child(11) input[type="radio"]+label span.sg-icon-target::before {
        background: #568528;
    }

    .sg-table tbody tr td:nth-child(12) input[type="radio"]+label span.sg-icon-target::before {
        background: #207716;
        border-radius: 0 10px 10px 0;
    }

    /* spécifique pour le non concerné */
    .sg-table tbody tr td:nth-child(13) input[type="radio"]+label span.sg-icon-target::before {
        background: #FFF;
        margin-left: 5px;
        margin-top: 5px;
        padding: 0 5px;
        content: 'Non concerné(e)';
        width: auto;
        font-size: 13px;
        font-weight: normal !important;
    }



    /* hover pour les éléments de la table */
    .sg-table tbody tr td:nth-child(2) input[type="radio"]:checked+label span.sg-icon-target::before,
    .sg-table tbody tr td:nth-child(2) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);


    }


    .sg-table tbody tr td:nth-child(3) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(3) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(4) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(4) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(5) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(5) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(6) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(6) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(7) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(7) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(8) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(8) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(9) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(9) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(10) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(10) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(11) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(11) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(12) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(12) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 0.8;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
        content: counter(css-counter);

    }

    .sg-table tbody tr td:nth-child(13) input[type="radio"]:checked+label span.sg-icon-target:before,
    .sg-table tbody tr td:nth-child(13) input[type="radio"]:hover+label span.sg-icon-target:before {
        opacity: 1;
        box-shadow: rgb(0 49 52 / 25%) 0px 5px 16px;
        background-color: rgb(243, 245, 243);
        border-radius: 5px;
        outline: 2px dotted green !important;
        font-size: 13px !important;
        color: black !important;
        transform: scale(1);

    }

    .sg-table tbody tr td input[type="radio"]:checked+label span.sg-icon-target:before {
        color: yellow;
        transform: scale(1.1);
    }



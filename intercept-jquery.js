// Script pour intercepter la création des éléments jQuery et les modifier à la volée
// À charger AVANT l'autre script jQuery

console.log('🚀 Script d\'interception jQuery chargé');

// Configuration des textes
const textConfig = {
    pasdutout: {
        leftText: "Pas du tout satisfait(e) 😟",
        rightText: "Très satisfait(e) 😍"
    },
    nondutout: {
        leftText: "Non, pas du tout 😟",
        rightText: "Oui, tout à fait 😍"
    },
    attention: {
        leftText: "Pas attentionnées 😟",
        rightText: "Très attentionnées 😍"
    }
};

// Fonction pour déterminer le type de page
function getPageType() {
    if (document.querySelector('.pasdutout')) return 'pasdutout';
    if (document.querySelector('.nondutout')) return 'nondutout';
    if (document.querySelector('.attention')) return 'attention';
    return 'pasdutout'; // Par défaut
}

// Attendre que jQuery soit disponible
function waitForJQuery() {
    if (typeof $ !== 'undefined' && typeof $.fn !== 'undefined') {
        console.log('✅ jQuery détecté, installation de l\'interception...');
        setupJQueryInterception();
    } else {
        console.log('⏳ Attente de jQuery...');
        setTimeout(waitForJQuery, 100);
    }
}

function setupJQueryInterception() {
    // Sauvegarder la méthode originale
    const originalBefore = $.fn.before;
    
    // Intercepter la méthode .before() de jQuery
    $.fn.before = function(...args) {
        console.log('🔍 Interception de .before() détectée');
        
        // Modifier les arguments si c'est un élément .header-band
        const modifiedArgs = args.map(arg => {
            if (typeof arg === 'string' && arg.includes('header-band')) {
                console.log('📝 Élément header-band détecté dans la chaîne');
                return modifyHeaderBandString(arg);
            } else if (arg && arg.jquery && arg.hasClass && arg.hasClass('header-band')) {
                console.log('📝 Élément jQuery header-band détecté');
                return modifyHeaderBandElement(arg);
            }
            return arg;
        });
        
        // Appeler la méthode originale avec les arguments modifiés
        const result = originalBefore.apply(this, modifiedArgs);
        
        // Vérifier après coup si des éléments header-band ont été ajoutés
        setTimeout(() => {
            checkAndFixHeaderBands();
        }, 10);
        
        return result;
    };
    
    console.log('✅ Interception jQuery installée');
}

function modifyHeaderBandString(htmlString) {
    const pageType = getPageType();
    const config = textConfig[pageType];
    
    console.log(`🔧 Modification de la chaîne HTML pour type: ${pageType}`);
    
    // Remplacer les textes dans la chaîne HTML
    let modifiedString = htmlString
        .replace(/Pas du tout satisfait\(e\) 😟/g, config.leftText)
        .replace(/Très satisfait\(e\) 😍/g, config.rightText);
    
    console.log('✅ Chaîne HTML modifiée');
    return modifiedString;
}

function modifyHeaderBandElement(jqueryElement) {
    const pageType = getPageType();
    const config = textConfig[pageType];
    
    console.log(`🔧 Modification de l'élément jQuery pour type: ${pageType}`);
    
    // Modifier les textes dans l'élément jQuery
    jqueryElement.find('.left-green-text').text(config.leftText);
    jqueryElement.find('.right-red-text').text(config.rightText);
    
    console.log('✅ Élément jQuery modifié');
    return jqueryElement;
}

function checkAndFixHeaderBands() {
    const pageType = getPageType();
    const config = textConfig[pageType];
    
    // Chercher tous les éléments header-band récemment ajoutés
    const headerBands = document.querySelectorAll('.header-band');
    
    headerBands.forEach((headerBand, index) => {
        const leftText = headerBand.querySelector('.left-green-text');
        const rightText = headerBand.querySelector('.right-red-text');
        
        if (leftText && rightText) {
            const currentLeft = leftText.textContent;
            const currentRight = rightText.textContent;
            
            // Vérifier si les textes sont incorrects
            if (currentLeft !== config.leftText || currentRight !== config.rightText) {
                console.log(`🔧 Correction du header-band ${index + 1}`);
                console.log(`   Avant: "${currentLeft}" / "${currentRight}"`);
                
                leftText.textContent = config.leftText;
                rightText.textContent = config.rightText;
                
                // Effet visuel pour confirmer
                leftText.style.backgroundColor = 'lightgreen';
                rightText.style.backgroundColor = 'lightcoral';
                
                setTimeout(() => {
                    leftText.style.backgroundColor = '';
                    rightText.style.backgroundColor = '';
                }, 1000);
                
                console.log(`   Après: "${config.leftText}" / "${config.rightText}"`);
            }
        }
    });
}

// Démarrer l'interception
waitForJQuery();

// Vérification continue en backup
setInterval(() => {
    checkAndFixHeaderBands();
}, 1000);

console.log('🎯 Script d\'interception prêt!');

/* CSS pour forcer l'affichage des bons textes selon le type de page */

/* Pour les pages de type "pasdutout" */
.pasdutout .left-green-text {
    font-size: 0 !important;
}

.pasdutout .left-green-text::after {
    content: "Pas du tout satisfait(e) 😟" !important;
    font-size: 14px !important;
    display: inline !important;
}

.pasdutout .right-red-text {
    font-size: 0 !important;
}

.pasdutout .right-red-text::after {
    content: "Très satisfait(e) 😍" !important;
    font-size: 14px !important;
    display: inline !important;
}

/* Pour les pages de type "nondutout" */
.nondutout .left-green-text {
    font-size: 0 !important;
}

.nondutout .left-green-text::after {
    content: "Non, pas du tout 😟" !important;
    font-size: 14px !important;
    display: inline !important;
}

.nondutout .right-red-text {
    font-size: 0 !important;
}

.nondutout .right-red-text::after {
    content: "<PERSON><PERSON>, tout à fait 😍" !important;
    font-size: 14px !important;
    display: inline !important;
}

/* Pour les pages de type "attention" */
.attention .left-green-text {
    font-size: 0 !important;
}

.attention .left-green-text::after {
    content: "Pas attentionnées 😟" !important;
    font-size: 14px !important;
    display: inline !important;
}

.attention .right-red-text {
    font-size: 0 !important;
}

.attention .right-red-text::after {
    content: "Très attentionnées 😍" !important;
    font-size: 14px !important;
    display: inline !important;
}

/* Style pour les effets visuels de debug */
.debug-highlight {
    border: 2px solid red !important;
    background-color: yellow !important;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

* {
  font-family: "Dosis", serif;
  font-optical-sizing: auto;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  color: #00564B;

}

h2 {
  font-size: 2rem;
  text-transform: uppercase;
}

h3 {
  font-size: 1.5rem;
}

.grillePrincipale {
  width: 97vw;
  height: 98vh;
  margin: auto;
  display: flex;
  justify-content: space-between;
  gap: 15px;
  overflow-y: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-top: 35px !important;
  margin-bottom: 25px !important;
  margin-bottom: 15px;
  width: 97vw;
  margin: auto;

}

.grand-titre {
  font-size: 2.7em;
}

.titre-moyen {
  font-size: 1.5rem;
  font-weight: bold;
}

.petit-titre {
  font-size: 1.1rem;
  font-weight: bold;
}



.col {
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  gap: 25px;
  margin-top: 50px;
}

.card {
  display: flex;
  flex-direction: column;
  border-radius: 15px;
  overflow: hidden;
  border: 1px solid rgb(147, 190, 147);

}

.entete {
  background-color: #00564B;
  display: flex;
  justify-content: space-between;
  height: fit-content;
  padding: 10px 15px;
}

.iconCo {
  display: flex;
  align-items: center;
  gap: 8px;

}

.iconCo img {
  width: 35px;
  height: 35px;
}

.iconCo p {
  color: white;
  font-size: 1rem;
  display: flex;
  flex-direction: column;
  line-height: 20px;
  font-weight: bold;
  text-transform: uppercase;
}

.iconCo p span {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;

}

.repondant {
  color: white;
  font-size: 1.5rem;
  display: flex;
  flex-direction: column;
  line-height: 25px;
  font-weight: bold;
  text-transform: uppercase;
  border-left: 1px solid rgb(62, 109, 93);
  padding-left: 25px;
  padding-right: 35px;

}

.repondant span {
  color: white;
  font-size: 1rem;
  font-weight: bold;
}

.score {
  color: #00564B;
}

.embed {
  width: 80%;
  max-height: 150px;
  height: 150px;
  background-color: aliceblue;
  margin: auto;
  margin-top: 10px;
  margin-bottom: 10px;
}

.score {
  margin-left: 55px;
  font-size: 2.7rem;
  font-weight: bold;
}

.score span {
  font-size: 1rem;
}



/* widget */
.conteneur {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.widget-container {
  display: flex;
  gap: 4px;
}

.widget {
  display: flex;
  width: 100%;
  border-radius: 15px;
  overflow: hidden;
  border: 1px solid rgb(147, 190, 147);
}

.w-icon {
  background-color: #00564B;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 50%;
}

.w-icon img {
  width: 35px;
  height: 35px;
}

.w-icon p {
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  text-align: center;
}


.score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.w-score {
  font-size: 2.7rem;
  font-weight: bold;
}

.w-score span {
  font-size: 1rem;
}


.w-repondant {
  color: #00564B;
  font-size: 1.2rem;
  line-height: 25px;
  font-weight: bold;
  text-transform: uppercase;

}

.w-repondant span {
  color: #00564B;
  font-size: 0.8rem;
  font-weight: bold;
  margin-left: 5px;
}


/* 3e colonne */
.thirdCol {
  background-color: rgba(255, 228, 196, 0.212);
  border-radius: 15px;
  position: relative;
  padding-top: 25px;
  margin-top: 0;
}

.couple {
  position: absolute;
  width: 50vh;
  bottom: 1vh;
}

.green-card-container-boy {
  position: absolute;
  bottom: 57vh;
  left: 2vw;
}

.green-card-boy,
.green-card-girl {
  height: fit-content !important;
}

.green-card-boy {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20px;
  width: 15vw;
  height: fit-content;
  background-color: #8bb026;
  border-radius: 15px;
  padding-bottom: 35px;
  padding-top: 25px;
}

.green-card-boy p,
.green-card-girl p {
  color: white;
  font-weight: 700;
  z-index: 500;
}

.green-card-boy .ratings,
.green-card-boy .ratings .score,
.green-card-boy .ratings .score span {
  color: white;
}

.green-card-girl .ratings,
.green-card-girl .ratings .score,
.green-card-girl .ratings .score span {
  color: white;
}

.ratings .score {
  font-size: 1.2rem;
  display: flex;
  align-items: baseline;
}

.ratings .score div {
  margin-left: 15px;
}

.ratings .score img {
  width: 15px;
}

.ratings .score span {
  font-size: 0.8rem;
}

.ratings {
  position: absolute;
  bottom: 5px;
  right: 20px
}


.cursor-boy {
  position: absolute;
  bottom: -5px;
  left: 30px;
  width: 2vh;
  height: 2vh;
  background-color: #8bb026;
  transform: rotate(-40deg);
}

.titre-google {
  color: #8bb026;
  text-align: center;
  margin-top: 25px;
}


.green-card-container-girl {
  position: absolute;
  bottom: 35vh;
  left: 22vh;
}

.green-card-girl {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20px;
  width: fit-content;
  height: fit-content;
  background-color: #8bb026;
  border-radius: 15px;
  padding-bottom: 35px;
  padding-top: 25px;
}


.cursor-girl {
  position: absolute;
  bottom: 15px;
  left: -5px;
  width: 2vh;
  height: 2vh;
  background-color: #8bb026;
  transform: rotate(-40deg);
}

.note-glo {
  position: absolute;
  right: 15px;
  top: 15vh;
  width: 13vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;


}

.note-glo .score {
  margin-left: 0;
}

.note-glo h3,
.note-glo .score,
.note-glo .score span {
  color: #8bb026;
  text-transform: uppercase;
}

.note-glo img {
  width: 30px;
}

.desc {
  margin-top: 10px;
}

.desc .note {
  font-size: 1.5rem;
}

.note-glo .desc p,
.note-glo .desc span {
  color: #8bb026;
  text-transform: uppercase;
  text-align: center;
  font-weight: 700;
}


.botanique-logo img {
  position: absolute;
  bottom: 10px;
  right: 15px;
  width: 10vw;
}
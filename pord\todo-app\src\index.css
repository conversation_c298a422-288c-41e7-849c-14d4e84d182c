:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  display: flex;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f5f5f5;
  color: #333;
}

#root {
  width: 100%;
}

button {
  font-family: inherit;
  cursor: pointer;
  transition: all 0.3s ease;
}

button:focus {
  outline: none;
}

input {
  font-family: inherit;
  outline: none;
}

/* Responsive design */
@media (max-width: 600px) {
  .todo-form {
    flex-direction: column;
  }

  .todo-form input {
    border-radius: 4px;
    margin-bottom: 0.5rem;
  }

  .todo-form button {
    border-radius: 4px;
  }

  .todo-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .todo-item input[type="checkbox"] {
    margin-bottom: 0.5rem;
  }

  .todo-item span {
    margin-bottom: 0.5rem;
    width: 100%;
  }

  .timer-container {
    padding: 0.8rem;
  }

  .timer-display {
    font-size: 1.2rem;
    margin-bottom: 0.8rem;
  }

  .timer-controls {
    flex-direction: column;
    gap: 0.5rem;
  }

  .timer-button {
    width: 100%;
  }
}

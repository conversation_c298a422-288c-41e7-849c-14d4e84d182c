import { useState, useEffect, useRef } from 'react'
import './App.css'

// Composant pour le chronomètre
function Timer({ todo, startTimer, pauseTimer, resetTimer }) {
  // Formater le temps en heures:minutes:secondes
  const formatTime = (timeInSeconds) => {
    // S'assurer que timeInSeconds est un nombre valide
    const time = typeof timeInSeconds === 'number' && !isNaN(timeInSeconds) ? timeInSeconds : 0;

    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = time % 60;

    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      seconds.toString().padStart(2, '0')
    ].join(':');
  };

  // S'assurer que todo a les propriétés nécessaires
  const timeSpent = todo && typeof todo.timeSpent === 'number' ? todo.timeSpent : 0;
  const useCountdown = todo && todo.useCountdown === true;
  const remainingTime = todo && typeof todo.remainingTime === 'number' ? todo.remainingTime : 0;

  // Déterminer quel temps afficher (temps écoulé ou temps restant)
  const displayTime = useCountdown ? remainingTime : timeSpent;

  // Déterminer la classe CSS pour le mode compte à rebours
  const timerClass = useCountdown
    ? `timer-display countdown ${remainingTime < 300 ? 'warning' : ''}`
    : 'timer-display';

  return (
    <div className="timer-container">
      <div className={timerClass}>
        {useCountdown && <span className="timer-mode">Compte à rebours: </span>}
        {formatTime(displayTime)}
      </div>

      {useCountdown && todo.countdownTime > 0 && (
        <div className="progress-bar">
          <div
            className="progress"
            style={{ width: `${(remainingTime / todo.countdownTime) * 100}%` }}
          ></div>
        </div>
      )}

      <div className="timer-controls">
        {!todo.isTimerActive ? (
          <button
            className="timer-button start"
            onClick={() => startTimer(todo.id)}
          >
            {(useCountdown ? remainingTime : timeSpent) > 0 ? 'Reprendre' : 'Démarrer'}
          </button>
        ) : (
          <button
            className="timer-button pause"
            onClick={() => pauseTimer(todo.id)}
          >
            Pause
          </button>
        )}
        <button
          className="timer-button reset"
          onClick={() => resetTimer(todo.id)}
          disabled={(useCountdown ? remainingTime === todo.countdownTime : timeSpent === 0)}
        >
          Réinitialiser
        </button>
      </div>
    </div>
  );
}

// Composant pour éditer une tâche
function TodoEditForm({ todo, saveEdits, cancelEditing }) {
  const [text, setText] = useState(todo.text);
  const [description, setDescription] = useState(todo.description || '');
  const [category, setCategory] = useState(todo.category || 'Email');
  const [client, setClient] = useState(todo.client || 'Interne');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (text.trim() !== '') {
      saveEdits(todo.id, text, description, category, client);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="todo-edit-form">
      <div className="form-row">
        <input
          type="text"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Titre de la tâche..."
          className="todo-input"
          required
        />
      </div>

      <div className="form-row">
        <textarea
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Description (optionnelle)..."
          className="todo-textarea"
          rows="3"
        />
      </div>

      <div className="form-row categories">
        <div className="form-group">
          <label>Catégorie:</label>
          <select
            value={category}
            onChange={(e) => setCategory(e.target.value)}
            className="category-select"
          >
            <option value="Email">Email</option>
            <option value="Questionnaire">Questionnaire</option>
            <option value="Investigation">Investigation</option>
          </select>
        </div>

        <div className="form-group">
          <label>Client:</label>
          <select
            value={client}
            onChange={(e) => setClient(e.target.value)}
            className="client-select"
          >
            <option value="Interne">Interne</option>
            <option value="Client A">Client A</option>
            <option value="Client B">Client B</option>
            <option value="Client C">Client C</option>
          </select>
        </div>
      </div>

      <div className="edit-buttons">
        <button type="submit" className="save-button">Enregistrer</button>
        <button type="button" onClick={() => cancelEditing(todo.id)} className="cancel-button">Annuler</button>
      </div>
    </form>
  );
}

// Fonction pour formater une date
function formatDate(dateString) {
  if (!dateString) return '';

  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// Fonction pour formater le temps en heures:minutes:secondes
function formatTime(timeInSeconds) {
  // S'assurer que timeInSeconds est un nombre valide
  const time = typeof timeInSeconds === 'number' && !isNaN(timeInSeconds) ? timeInSeconds : 0;

  const hours = Math.floor(time / 3600);
  const minutes = Math.floor((time % 3600) / 60);
  const seconds = time % 60;

  return [
    hours.toString().padStart(2, '0'),
    minutes.toString().padStart(2, '0'),
    seconds.toString().padStart(2, '0')
  ].join(':');
}

// Composant pour afficher un élément de la liste
function TodoItem({ todo, toggleComplete, deleteTodo, startTimer, pauseTimer, resetTimer, startEditing, saveEdits, cancelEditing, toggleExpand, switchToTask }) {
  // Si la tâche est en mode édition, afficher le formulaire d'édition
  if (todo.isEditing) {
    return (
      <div className={`todo-item ${todo.completed ? 'completed' : ''}`}>
        <TodoEditForm
          todo={todo}
          saveEdits={saveEdits}
          cancelEditing={cancelEditing}
        />
      </div>
    );
  }

  // Sinon, afficher la tâche normalement
  return (
    <div className={`todo-item ${todo.completed ? 'completed' : ''} ${todo.isActive ? 'active' : ''}`}>
      <div className="todo-item-header" onClick={() => toggleExpand(todo.id)}>
        <div className="todo-header-left">
          <span className={`expand-icon ${todo.isExpanded ? 'expanded' : ''}`}>
            {todo.isExpanded ? '▼' : '▶'}
          </span>
          <input
            type="checkbox"
            checked={todo.completed}
            onChange={(e) => {
              e.stopPropagation(); // Empêcher le clic de déclencher toggleExpand
              toggleComplete(todo.id);
            }}
          />
          <div className="todo-header-info">
            <div className="todo-title-timer">
              <div className="todo-text">
                <span className="client-prefix">{todo.client.toUpperCase()}</span>
                <span className="title-separator"> | </span>
                <span className="task-title">{todo.text}</span>
              </div>
              <div className="inline-timer">
                {formatTime(todo.useCountdown ? todo.remainingTime : todo.timeSpent)}
                {todo.isTimerActive && <span className="timer-active-indicator"></span>}
              </div>
            </div>
            <div className="todo-meta">
              {todo.category && (
                <span className={`todo-category ${todo.category.toLowerCase()}`}>{todo.category}</span>
              )}
              {todo.client && (
                <span className="todo-client">{todo.client}</span>
              )}
              <span className="todo-date">Créée le {formatDate(todo.createdAt)}</span>
              {todo.completed && todo.completedAt && (
                <span className="todo-date completed">Terminée le {formatDate(todo.completedAt)}</span>
              )}
            </div>
          </div>
        </div>

        <div className="todo-header-actions" onClick={(e) => e.stopPropagation()}>
          <button
            onClick={() => switchToTask(todo.id)}
            className={`action-button switch-button ${todo.isActive ? 'active' : ''}`}
            title="Basculer ici"
            disabled={todo.isActive}
          >
            {todo.isActive ? '🟢' : '⏳'}
          </button>
          <button
            onClick={() => startEditing(todo.id)}
            className="action-button edit-button"
            title="Modifier"
          >
            ✏️
          </button>
          <button
            onClick={() => deleteTodo(todo.id)}
            className="action-button delete-button"
            title="Supprimer"
          >
           
            ❌

            
          </button>
        </div>
      </div>

      {todo.isExpanded && (
        <div className="todo-item-body">
          {todo.description ? (
            <div className="todo-description">
              <h4>Description</h4>
              <div className="description-content">{todo.description}</div>
            </div>
          ) : (
            <div className="todo-description empty">
              <p>Aucune description</p>
            </div>
          )}

          <Timer
            todo={todo}
            startTimer={startTimer}
            pauseTimer={pauseTimer}
            resetTimer={resetTimer}
          />
        </div>
      )}
    </div>
  );
}

// Composant pour gérer la liste des clients
function ClientManager({ clients, setClients, onClose }) {
  const [newClient, setNewClient] = useState('');
  const [searchClient, setSearchClient] = useState('');

  // Ajouter un nouveau client
  const addClient = () => {
    if (newClient.trim() !== '' && !clients.includes(newClient.trim())) {
      setClients([...clients, newClient.trim()]);
      setNewClient('');
    }
  };

  // Supprimer un client
  const removeClient = (clientToRemove) => {
    if (clientToRemove === 'Interne') {
      alert('Le client "Interne" ne peut pas être supprimé.');
      return;
    }
    setClients(clients.filter(client => client !== clientToRemove));
  };

  // Filtrer les clients par recherche
  const filteredClients = clients.filter(client =>
    client.toLowerCase().includes(searchClient.toLowerCase())
  );

  return (
    <div className="client-manager">
      <div className="client-manager-header">
        <h2>Gestion des clients</h2>
        <button onClick={onClose} className="close-button">×</button>
      </div>

      <div className="client-add-form">
        <input
          type="text"
          value={newClient}
          onChange={(e) => setNewClient(e.target.value)}
          placeholder="Nom du nouveau client..."
          className="client-input"
        />
        <button onClick={addClient} className="add-client-button">Ajouter</button>
      </div>

      <div className="client-search">
        <input
          type="text"
          value={searchClient}
          onChange={(e) => setSearchClient(e.target.value)}
          placeholder="Rechercher un client..."
          className="search-client-input"
        />
      </div>

      <div className="client-list">
        {filteredClients.length === 0 ? (
          <p className="no-clients">Aucun client trouvé.</p>
        ) : (
          filteredClients.map(client => (
            <div key={client} className="client-item">
              <span>{client}</span>
              <button
                onClick={() => removeClient(client)}
                className="remove-client-button"
                disabled={client === 'Interne'}
                title={client === 'Interne' ? 'Ce client ne peut pas être supprimé' : 'Supprimer ce client'}
              >
                ×
              </button>
            </div>
          ))
        )}
      </div>
    </div>
  );
}

// Composant pour le formulaire d'ajout de tâche
function TodoForm({ addTodo, clients, onManageClients }) {
  const [text, setText] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('Email');
  const [client, setClient] = useState('Interne');
  const [useCountdown, setUseCountdown] = useState(false);
  const [countdownHours, setCountdownHours] = useState(0);
  const [countdownMinutes, setCountdownMinutes] = useState(0);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (text.trim() !== '') {
      // Calculer le temps total en secondes pour le compte à rebours
      const countdownTime = useCountdown
        ? (countdownHours * 3600) + (countdownMinutes * 60)
        : 0;

      addTodo(text, description, category, client, useCountdown, countdownTime);

      // Réinitialiser le formulaire
      setText('');
      setDescription('');
      setCategory('Email');
      setClient('Interne');
      setUseCountdown(false);
      setCountdownHours(0);
      setCountdownMinutes(0);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="todo-form">
      <div className="form-row">
        <input
          type="text"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Titre de la tâche..."
          className="todo-input"
          required
        />
      </div>

      <div className="form-row">
        <textarea
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Description (optionnelle)..."
          className="todo-textarea"
          rows="3"
        />
      </div>

      <div className="form-row categories">
        <div className="form-group">
          <label>Catégorie:</label>
          <select
            value={category}
            onChange={(e) => setCategory(e.target.value)}
            className="category-select"
          >
            <option value="Email">Email</option>
            <option value="Questionnaire">Questionnaire</option>
            <option value="Investigation">Investigation</option>
          </select>
        </div>

        <div className="form-group client-group">
          <div className="client-label-row">
            <label>Client:</label>
            <button
              type="button"
              onClick={onManageClients}
              className="manage-clients-button"
              title="Gérer les clients"
              aria-label="Gérer les clients"
            >
              ⚙️
            </button>
          </div>
          <select
            value={client}
            onChange={(e) => setClient(e.target.value)}
            className="client-select"
          >
            {clients.map(clientOption => (
              <option key={clientOption} value={clientOption}>{clientOption}</option>
            ))}
          </select>
        </div>
      </div>

      <div className="form-row countdown-row">
        <div className="form-group checkbox-group">
          <input
            type="checkbox"
            id="use-countdown"
            checked={useCountdown}
            onChange={(e) => setUseCountdown(e.target.checked)}
            className="countdown-checkbox"
          />
          <label htmlFor="use-countdown">Utiliser un compte à rebours</label>
        </div>

        {useCountdown && (
          <div className="countdown-inputs">
            <div className="time-input">
              <input
                type="number"
                min="0"
                max="23"
                value={countdownHours}
                onChange={(e) => setCountdownHours(parseInt(e.target.value) || 0)}
                className="time-number"
              />
              <label>h</label>
            </div>
            <div className="time-input">
              <input
                type="number"
                min="0"
                max="59"
                value={countdownMinutes}
                onChange={(e) => setCountdownMinutes(parseInt(e.target.value) || 0)}
                className="time-number"
              />
              <label>min</label>
            </div>
          </div>
        )}
      </div>

      <button type="submit" className="add-button">Ajouter</button>
    </form>
  );
}

// Liste initiale des clients
const initialClients = [
  'Interne',
  'Acadomia',
  'ANAH',
  'Anset',
  'Areas Assurances',
  'Auchan',
  'Auchan RH',
  'Auror',
  'Autobacs',
  'Avril',
  'AXA France',
  'Banque Française Mutualiste',
  'Beko France',
  'Belambra',
  'BforBank',
  'Blank',
  'Botanic',
  'BoursoBank',
  'BUT International',
  'CACF',
  'Céline',
  'Certigna',
  'CNAV',
  'Colmarienne des Eaux',
  'Comdata France  / BMW',
  'Comdata France  / FORD',
  'Comdata Spa / EGSS',
  'Coyote',
  'CRM Services',
  'Delubac',
  'EDF solutions solaires',
  'EDF via CSA',
  'ELIS',
  'Enedis',
  'Flash client / Konecta',
  'Fraikin',
  'Française des Jeux',
  'Galeries Lafayette Haussmann',
  'Groupe Rossel',
  'Hema',
  'Henner International',
  'Homair (Vacanceselect)',
  'Hôpital Américain de Paris',
  'Konecta groupe / client survey',
  'Krones',
  'Kuoni',
  'La Méridionale',
  'La Provence',
  'Léon',
  'Lille Grand Palais',
  'Ma French Bank',
  'MK2 Hotel Paradiso',
  'Nutrition & santé',
  'Odyssey',
  'Onet',
  'Paris 2024',
  'Partenord Habitat',
  'Primagaz',
  'Primavista',
  'Pro BTP',
  'Salaun Holidays',
  'Salti',
  'Santéclair',
  'Shiva Groupe',
  'SNCF (Obsession Client)',
  'Société Générales Assurances',
  'Suez',
  'Teksial',
  'Transatel',
  'Trenitalia',
  'Unifitel',
  'Valophis',
  'Venchi',
  'Villages clubs du soleil',
  'Worldline'
];

function App() {
  // État pour les filtres et la recherche
  const [searchTerm, setSearchTerm] = useState('');
  const [clientFilter, setClientFilter] = useState('Tous');
  const [showClientManager, setShowClientManager] = useState(false);

  // État pour la liste des clients
  const [clients, setClients] = useState(() => {
    const savedClients = localStorage.getItem('clients');
    if (savedClients) {
      return JSON.parse(savedClients);
    } else {
      return initialClients;
    }
  });

  // Sauvegarder les clients dans localStorage quand ils changent
  useEffect(() => {
    localStorage.setItem('clients', JSON.stringify(clients));
  }, [clients]);

  const [todos, setTodos] = useState(() => {
    // Récupérer les todos du localStorage s'ils existent
    const savedTodos = localStorage.getItem('todos');
    if (savedTodos) {
      // S'assurer que toutes les tâches ont une catégorie
      const parsedTodos = JSON.parse(savedTodos);
      return parsedTodos.map(todo => ({
        ...todo,
        category: todo.category || 'Email', // Ajouter une catégorie par défaut si elle n'existe pas
        timeSpent: typeof todo.timeSpent === 'number' ? todo.timeSpent : 0, // S'assurer que timeSpent est un nombre
        isTimerActive: Boolean(todo.isTimerActive) // S'assurer que isTimerActive est un booléen
      }));
    } else {
      return [];
    }
  });

  // Référence pour l'intervalle de mise à jour des chronomètres
  const timerIntervalRef = useRef(null);

  // Sauvegarder les todos dans localStorage quand ils changent
  useEffect(() => {
    localStorage.setItem('todos', JSON.stringify(todos));
  }, [todos]);

  // Configurer l'intervalle pour mettre à jour les chronomètres actifs
  useEffect(() => {
    // Vérifier s'il y a des chronomètres actifs
    const hasActiveTimers = todos.some(todo => todo.isTimerActive);

    // Si des chronomètres sont actifs et qu'aucun intervalle n'est en cours
    if (hasActiveTimers && !timerIntervalRef.current) {
      timerIntervalRef.current = setInterval(() => {
        setTodos(prevTodos =>
          prevTodos.map(todo => {
            if (!todo.isTimerActive) return todo;

            // Si c'est un compte à rebours
            if (todo.useCountdown) {
              const newRemainingTime = Math.max(0, todo.remainingTime - 1);

              // Si le compte à rebours atteint zéro, arrêter le chronomètre
              if (newRemainingTime === 0 && todo.isTimerActive) {
                return {
                  ...todo,
                  remainingTime: newRemainingTime,
                  isTimerActive: false
                };
              }

              return { ...todo, remainingTime: newRemainingTime };
            }
            // Sinon, c'est un chronomètre normal
            else {
              return { ...todo, timeSpent: todo.timeSpent + 1 };
            }
          })
        );
      }, 1000); // Mettre à jour chaque seconde
    }
    // Si aucun chronomètre n'est actif mais qu'un intervalle est en cours
    else if (!hasActiveTimers && timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current);
      timerIntervalRef.current = null;
    }

    // Nettoyer l'intervalle lors du démontage du composant
    return () => {
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
        timerIntervalRef.current = null;
      }
    };
  }, [todos]);

  // Ajouter une nouvelle tâche
  const addTodo = (text, description, category, client, useCountdown, countdownTime) => {
    const now = new Date();
    const newTodo = {
      id: Date.now(),
      text,
      description: description || '', // Description de la tâche
      category, // Catégorie de la tâche (Email, Questionnaire, Investigation)
      client, // Client assigné à la tâche
      completed: false,
      timeSpent: 0, // Temps passé sur la tâche en secondes
      isTimerActive: false, // État du chronomètre (actif ou en pause)
      useCountdown, // Utiliser le mode compte à rebours
      countdownTime, // Temps total pour le compte à rebours (en secondes)
      remainingTime: countdownTime, // Temps restant pour le compte à rebours (en secondes)
      isEditing: false, // Mode édition pour modifier le titre et la description
      isExpanded: false, // État déplié/replié de la tâche
      isActive: false, // Indique si cette tâche est actuellement active (focus)
      createdAt: now.toISOString(), // Date de création
      completedAt: null, // Date de clôture (null si non terminée)
    };
    setTodos([...todos, newTodo]);
  };

  // Basculer l'état d'une tâche (complétée ou non)
  const toggleComplete = (id) => {
    const now = new Date();
    setTodos(todos.map(todo => {
      if (todo.id !== id) return todo;

      // Si la tâche est marquée comme terminée, enregistrer la date de clôture
      // Sinon, réinitialiser la date de clôture à null
      const newCompleted = !todo.completed;
      return {
        ...todo,
        completed: newCompleted,
        completedAt: newCompleted ? now.toISOString() : null
      };
    }));
  };

  // Supprimer une tâche
  const deleteTodo = (id) => {
    // Si le chronomètre de cette tâche est actif, on le désactive avant de supprimer
    const todoToDelete = todos.find(todo => todo.id === id);
    if (todoToDelete && todoToDelete.isTimerActive) {
      pauseTimer(id);
    }
    setTodos(todos.filter(todo => todo.id !== id));
  };

  // Activer le mode édition pour une tâche
  const startEditing = (id) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, isEditing: true } : todo
    ));
  };

  // Enregistrer les modifications d'une tâche
  const saveEdits = (id, newText, newDescription, newCategory, newClient) => {
    setTodos(todos.map(todo =>
      todo.id === id ? {
        ...todo,
        text: newText,
        description: newDescription,
        category: newCategory,
        client: newClient,
        isEditing: false
      } : todo
    ));
  };

  // Annuler l'édition d'une tâche
  const cancelEditing = (id) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, isEditing: false } : todo
    ));
  };

  // Basculer l'état déplié/replié d'une tâche
  const toggleExpand = (id) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, isExpanded: !todo.isExpanded } : todo
    ));
  };

  // Démarrer ou reprendre le chronomètre d'une tâche
  const startTimer = (id) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, isTimerActive: true } : todo
    ));
  };

  // Mettre en pause le chronomètre d'une tâche
  const pauseTimer = (id) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, isTimerActive: false } : todo
    ));
  };

  // Réinitialiser le chronomètre d'une tâche
  const resetTimer = (id) => {
    setTodos(todos.map(todo => {
      if (todo.id !== id) return todo;

      // Si c'est un compte à rebours, réinitialiser le temps restant
      if (todo.useCountdown) {
        return {
          ...todo,
          remainingTime: todo.countdownTime,
          isTimerActive: false
        };
      }
      // Sinon, réinitialiser le temps passé
      else {
        return {
          ...todo,
          timeSpent: 0,
          isTimerActive: false
        };
      }
    }));
  };

  // Basculer vers une tâche spécifique (mettre en pause toutes les autres)
  const switchToTask = (id) => {
    // D'abord, mettre à jour les états des tâches
    const updatedTodos = todos.map(todo => {
      // Si c'est la tâche sélectionnée
      if (todo.id === id) {
        return {
          ...todo,
          isTimerActive: true,
          isActive: true,
          // Ne pas modifier l'état déplié/replié
        };
      }
      // Pour toutes les autres tâches
      else {
        return {
          ...todo,
          isTimerActive: false,
          isActive: false
        };
      }
    });

    // Réorganiser les tâches pour placer la tâche active en haut
    const activeTask = updatedTodos.find(todo => todo.id === id);
    const otherTasks = updatedTodos.filter(todo => todo.id !== id);

    // Mettre à jour l'état avec la tâche active en premier
    setTodos([activeTask, ...otherTasks]);
  };

  // Filtrer les tâches en fonction des critères de recherche et de filtre
  const filteredTodos = todos.filter(todo => {
    // Filtrer par terme de recherche (titre ou description)
    const matchesSearch =
      todo.text.toLowerCase().includes(searchTerm.toLowerCase()) ||
      todo.description.toLowerCase().includes(searchTerm.toLowerCase());

    // Filtrer par client
    const matchesClient = clientFilter === 'Tous' || todo.client === clientFilter;

    return matchesSearch && matchesClient;
  });


  // Fonction pour ouvrir/fermer le gestionnaire de clients
  const toggleClientManager = () => {
    setShowClientManager(!showClientManager);
  };

  return (
    <div className="app-container">
      {showClientManager && (
        <div className="modal-overlay">
          <ClientManager
            clients={clients}
            setClients={setClients}
            onClose={toggleClientManager}
          />
        </div>
      )}

      <div className="sidebar">
        <h1>Ma Liste de Tâches</h1>
        <TodoForm
          addTodo={addTodo}
          clients={clients}
          onManageClients={toggleClientManager}
        />
      </div>

      <div className="main-content">
        <div className="filters">
          <div className="search-container">
            <input
              type="text"
              placeholder="Rechercher..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          <div className="filter-container">
            <label>Filtrer par client:</label>
            <div className="filter-client-row">
              <select
                value={clientFilter}
                onChange={(e) => setClientFilter(e.target.value)}
                className="client-filter"
              >
                <option value="Tous">Tous les clients</option>
                {clients.map(client => (
                  <option key={client} value={client}>{client}</option>
                ))}
              </select>
              <button
                onClick={toggleClientManager}
                className="manage-clients-button filter"
                title="Gérer les clients"
              >
                ⚙️
              </button>
            </div>
          </div>
        </div>

        <div className="todo-list">
          {filteredTodos.length === 0 ? (
            <p className="empty-message">
              {todos.length === 0
                ? "Aucune tâche pour le moment. Ajoutez-en une !"
                : "Aucune tâche ne correspond à vos critères de recherche."}
            </p>
          ) : (
            filteredTodos.map(todo => (
              <TodoItem
                key={todo.id}
                todo={todo}
                toggleComplete={toggleComplete}
                deleteTodo={deleteTodo}
                startTimer={startTimer}
                pauseTimer={pauseTimer}
                resetTimer={resetTimer}
                startEditing={startEditing}
                saveEdits={saveEdits}
                cancelEditing={cancelEditing}
                toggleExpand={toggleExpand}
                switchToTask={switchToTask}
              />
            ))
          )}
        </div>
      </div>
    </div>
  );
}

export default App;

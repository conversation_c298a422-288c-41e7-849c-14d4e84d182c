// Script pour vérifier tous les éléments avec la classe "pasdutout"
// Utilisation: Coller ce script dans la console du navigateur ou l'inclure dans une page

function checkPasdutoutElements() {
    console.log('🔍 Recherche des éléments avec la classe "pasdutout"...');
    console.log('================================================');

    // Sélectionner tous les éléments avec la classe "pasdutout"
    const elements = document.querySelectorAll('.pasdutout');

    console.log(`📊 Nombre total d'éléments trouvés: ${elements.length}`);
    console.log('');

    if (elements.length === 0) {
        console.log('❌ Aucun élément avec la classe "pasdutout" trouvé.');
        return;
    }

    // Logger chaque élément avec ses détails (version sécurisée)
    elements.forEach((element, index) => {
        try {
            console.log(`🎯 Élément ${index + 1}:`);
            console.log('  Tag:', element.tagName);
            console.log('  Classes:', element.className);
            console.log('  ID:', element.id || 'Aucun ID');

            // Contenu texte sécurisé
            const textContent = element.textContent ? element.textContent.trim() : '';
            console.log('  Contenu texte:', textContent.substring(0, 100) + (textContent.length > 100 ? '...' : ''));

            // HTML interne sécurisé
            const innerHTML = element.innerHTML || '';
            console.log('  HTML interne (début):', innerHTML.substring(0, 150) + (innerHTML.length > 150 ? '...' : ''));

            // Position sécurisée
            try {
                const rect = element.getBoundingClientRect();
                console.log('  Position:', `x:${Math.round(rect.x)}, y:${Math.round(rect.y)}, width:${Math.round(rect.width)}, height:${Math.round(rect.height)}`);
            } catch (e) {
                console.log('  Position: Non disponible');
            }

            // Styles principaux seulement
            try {
                const styles = window.getComputedStyle(element);
                console.log('  Display:', styles.display);
                console.log('  Visibility:', styles.visibility);
                console.log('  Background:', styles.backgroundColor);
            } catch (e) {
                console.log('  Styles: Non disponibles');
            }

            console.log('  Élément DOM:', element);
            console.log('  ─────────────────────────────────────────');
        } catch (error) {
            console.log(`❌ Erreur lors de l'analyse de l'élément ${index + 1}:`, error.message);
        }
    });

    console.log('✅ Analyse terminée!');

    // Retourner les éléments pour manipulation ultérieure
    return elements;
}

// Version simplifiée pour un logging rapide
function quickCheckPasdutout() {
    const elements = document.querySelectorAll('.pasdutout');
    console.log(`Éléments avec classe "pasdutout": ${elements.length}`);
    elements.forEach((el, i) => {
        const text = el.textContent ? el.textContent.trim() : '';
        console.log(`${i + 1}. ${el.tagName} - ${text.substring(0, 50)}${text.length > 50 ? '...' : ''}`);
    });
    return elements;
}

// Version ultra-simple sans erreurs
function simpleCheckPasdutout() {
    const elements = document.querySelectorAll('.pasdutout');
    console.log('=== ÉLÉMENTS PASDUTOUT ===');
    console.log('Nombre:', elements.length);

    for (let i = 0; i < elements.length; i++) {
        const el = elements[i];
        console.log(`\n${i + 1}. Tag: ${el.tagName}`);
        console.log(`   Classes: ${el.className}`);
        console.log(`   ID: ${el.id || 'Aucun'}`);

        // Contenu texte sécurisé
        try {
            const text = el.textContent || el.innerText || '';
            console.log(`   Texte: "${text.trim().substring(0, 80)}${text.length > 80 ? '...' : ''}"`);
        } catch (e) {
            console.log(`   Texte: Non accessible`);
        }

        // Attributs
        try {
            const attrs = [];
            for (let attr of el.attributes) {
                attrs.push(`${attr.name}="${attr.value}"`);
            }
            console.log(`   Attributs: ${attrs.join(', ')}`);
        } catch (e) {
            console.log(`   Attributs: Non accessibles`);
        }
    }

    console.log('\n=== FIN ===');
    return elements;
}

// Version avec highlighting visuel
function highlightPasdutoutElements() {
    const elements = document.querySelectorAll('.pasdutout');

    elements.forEach((element, index) => {
        // Ajouter un style de surbrillance temporaire
        element.style.outline = '3px solid red';
        element.style.backgroundColor = 'yellow';
        element.style.opacity = '0.8';

        // Ajouter un numéro pour identifier l'élément
        const label = document.createElement('div');
        label.textContent = `PASDUTOUT ${index + 1}`;
        label.style.position = 'absolute';
        label.style.top = '0';
        label.style.left = '0';
        label.style.backgroundColor = 'red';
        label.style.color = 'white';
        label.style.padding = '2px 5px';
        label.style.fontSize = '12px';
        label.style.fontWeight = 'bold';
        label.style.zIndex = '9999';
        label.className = 'pasdutout-label';

        element.style.position = 'relative';
        element.appendChild(label);
    });

    console.log(`✨ ${elements.length} éléments "pasdutout" mis en surbrillance`);

    // Fonction pour nettoyer la surbrillance
    window.clearPasdutoutHighlight = function() {
        elements.forEach(element => {
            element.style.outline = '';
            element.style.backgroundColor = '';
            element.style.opacity = '';
            const labels = element.querySelectorAll('.pasdutout-label');
            labels.forEach(label => label.remove());
        });
        console.log('🧹 Surbrillance nettoyée');
    };

    return elements;
}

// Auto-exécution si le script est chargé directement
if (typeof window !== 'undefined') {
    console.log('📋 Script de vérification des éléments "pasdutout" chargé!');
    console.log('💡 Utilisez ces fonctions:');
    console.log('   - checkPasdutoutElements() : Analyse complète');
    console.log('   - quickCheckPasdutout() : Analyse rapide');
    console.log('   - highlightPasdutoutElements() : Surbrillance visuelle');
    console.log('   - clearPasdutoutHighlight() : Nettoyer la surbrillance');
}

// Export pour utilisation en module (optionnel)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        checkPasdutoutElements,
        quickCheckPasdutout,
        highlightPasdutoutElements
    };
}

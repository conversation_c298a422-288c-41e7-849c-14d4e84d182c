// Script pour vérifier tous les éléments avec la classe "pasdutout"
// Utilisation: Coller ce script dans la console du navigateur ou l'inclure dans une page

function checkPasdutoutElements() {
    console.log('🔍 Recherche des éléments avec la classe "pasdutout"...');
    console.log('================================================');
    
    // Sélectionner tous les éléments avec la classe "pasdutout"
    const elements = document.querySelectorAll('.pasdutout');
    
    console.log(`📊 Nombre total d'éléments trouvés: ${elements.length}`);
    console.log('');
    
    if (elements.length === 0) {
        console.log('❌ Aucun élément avec la classe "pasdutout" trouvé.');
        return;
    }
    
    // Logger chaque élément avec ses détails
    elements.forEach((element, index) => {
        console.log(`🎯 Élément ${index + 1}:`);
        console.log('  Tag:', element.tagName);
        console.log('  Classes:', element.className);
        console.log('  ID:', element.id || 'Aucun ID');
        console.log('  Contenu texte:', element.textContent.trim().substring(0, 100) + (element.textContent.trim().length > 100 ? '...' : ''));
        console.log('  HTML interne (début):', element.innerHTML.substring(0, 150) + (element.innerHTML.length > 150 ? '...' : ''));
        console.log('  Position dans le DOM:', element.getBoundingClientRect());
        console.log('  Styles calculés:', window.getComputedStyle(element));
        console.log('  Élément DOM:', element);
        console.log('  ─────────────────────────────────────────');
    });
    
    console.log('✅ Analyse terminée!');
    
    // Retourner les éléments pour manipulation ultérieure
    return elements;
}

// Version simplifiée pour un logging rapide
function quickCheckPasdutout() {
    const elements = document.querySelectorAll('.pasdutout');
    console.log(`Éléments avec classe "pasdutout": ${elements.length}`);
    elements.forEach((el, i) => {
        console.log(`${i + 1}. ${el.tagName} - ${el.textContent.trim().substring(0, 50)}...`);
    });
    return elements;
}

// Version avec highlighting visuel
function highlightPasdutoutElements() {
    const elements = document.querySelectorAll('.pasdutout');
    
    elements.forEach((element, index) => {
        // Ajouter un style de surbrillance temporaire
        element.style.outline = '3px solid red';
        element.style.backgroundColor = 'yellow';
        element.style.opacity = '0.8';
        
        // Ajouter un numéro pour identifier l'élément
        const label = document.createElement('div');
        label.textContent = `PASDUTOUT ${index + 1}`;
        label.style.position = 'absolute';
        label.style.top = '0';
        label.style.left = '0';
        label.style.backgroundColor = 'red';
        label.style.color = 'white';
        label.style.padding = '2px 5px';
        label.style.fontSize = '12px';
        label.style.fontWeight = 'bold';
        label.style.zIndex = '9999';
        label.className = 'pasdutout-label';
        
        element.style.position = 'relative';
        element.appendChild(label);
    });
    
    console.log(`✨ ${elements.length} éléments "pasdutout" mis en surbrillance`);
    
    // Fonction pour nettoyer la surbrillance
    window.clearPasdutoutHighlight = function() {
        elements.forEach(element => {
            element.style.outline = '';
            element.style.backgroundColor = '';
            element.style.opacity = '';
            const labels = element.querySelectorAll('.pasdutout-label');
            labels.forEach(label => label.remove());
        });
        console.log('🧹 Surbrillance nettoyée');
    };
    
    return elements;
}

// Auto-exécution si le script est chargé directement
if (typeof window !== 'undefined') {
    console.log('📋 Script de vérification des éléments "pasdutout" chargé!');
    console.log('💡 Utilisez ces fonctions:');
    console.log('   - checkPasdutoutElements() : Analyse complète');
    console.log('   - quickCheckPasdutout() : Analyse rapide');
    console.log('   - highlightPasdutoutElements() : Surbrillance visuelle');
    console.log('   - clearPasdutoutHighlight() : Nettoyer la surbrillance');
}

// Export pour utilisation en module (optionnel)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        checkPasdutoutElements,
        quickCheckPasdutout,
        highlightPasdutoutElements
    };
}

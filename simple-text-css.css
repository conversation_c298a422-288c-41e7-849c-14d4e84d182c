/* Solution CSS simple pour forcer les bons textes */

/* Pages pasdutout */
.pasdutout .left-green-text { font-size: 0 !important; }
.pasdutout .left-green-text::after { content: "Pas du tout satisfait(e) 😟" !important; font-size: 14px !important; }
.pasdutout .right-red-text { font-size: 0 !important; }
.pasdutout .right-red-text::after { content: "Très satisfait(e) 😍" !important; font-size: 14px !important; }

/* Pages nondutout */
.nondutout .left-green-text { font-size: 0 !important; }
.nondutout .left-green-text::after { content: "Non, pas du tout 😟" !important; font-size: 14px !important; }
.nondutout .right-red-text { font-size: 0 !important; }
.nondutout .right-red-text::after { content: "Oui, tout à fait 😍" !important; font-size: 14px !important; }

/* Pages attention */
.attention .left-green-text { font-size: 0 !important; }
.attention .left-green-text::after { content: "Pas attentionnées 😟" !important; font-size: 14px !important; }
.attention .right-red-text { font-size: 0 !important; }
.attention .right-red-text::after { content: "Tr<PERSON> attentionnées 😍" !important; font-size: 14px !important; }

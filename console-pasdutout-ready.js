// Script pour console avec document ready
// Copiez-collez ce code dans la console du navigateur

function checkWhenReady() {
    function doCheck() {
        console.log('🔍 Vérification des éléments .pasdutout');
        
        const elements = document.querySelectorAll('.pasdutout');
        console.log(`📊 Nombre d'éléments trouvés: ${elements.length}`);
        
        if (elements.length === 0) {
            console.log('❌ Aucun élément avec la classe "pasdutout" trouvé');
            console.log('💡 Vérifiez que:');
            console.log('   - La page est complètement chargée');
            console.log('   - La classe "pasdutout" existe bien dans le HTML');
            console.log('   - Il n\'y a pas de faute de frappe dans le nom de classe');
            return [];
        }
        
        // Affichage détaillé de chaque élément
        elements.forEach((el, index) => {
            console.log(`\n🎯 Élément ${index + 1}:`);
            console.log(`   Tag: ${el.tagName}`);
            console.log(`   Classes: "${el.className}"`);
            console.log(`   ID: ${el.id || '(aucun)'}`)
            
            // Texte de l'élément
            try {
                const text = (el.textContent || el.innerText || '').trim();
                if (text) {
                    console.log(`   Texte: "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"`);
                } else {
                    console.log(`   Texte: (vide)`);
                }
            } catch (e) {
                console.log(`   Texte: (erreur d'accès)`);
            }
            
            // HTML de l'élément (début)
            try {
                const html = el.outerHTML;
                console.log(`   HTML: ${html.substring(0, 150)}${html.length > 150 ? '...' : ''}`);
            } catch (e) {
                console.log(`   HTML: (erreur d'accès)`);
            }
            
            // Référence à l'élément pour inspection
            console.log(`   Élément:`, el);
        });
        
        console.log('\n✅ Vérification terminée!');
        return elements;
    }
    
    // Vérifier l'état du DOM
    if (document.readyState === 'loading') {
        console.log('⏳ DOM en cours de chargement... Attente...');
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ DOM chargé! Exécution de la vérification...');
            doCheck();
        });
    } else {
        console.log('✅ DOM déjà chargé! Exécution immédiate...');
        doCheck();
    }
}

// Version ultra-simple pour test rapide
function quickTest() {
    if (document.readyState === 'loading') {
        console.log('❌ DOM pas encore chargé. Attendez ou utilisez checkWhenReady()');
        return;
    }
    
    const els = document.querySelectorAll('.pasdutout');
    console.log(`Éléments .pasdutout: ${els.length}`);
    els.forEach((el, i) => console.log(`${i+1}. ${el.tagName} - ${el.className}`, el));
    return els;
}

// Exécution automatique
console.log('🚀 Script de vérification pasdutout avec document ready');
console.log('📋 Fonctions disponibles:');
console.log('   - checkWhenReady() : Vérification avec attente du DOM');
console.log('   - quickTest() : Test rapide si DOM déjà chargé');
console.log('');
console.log('⚡ Exécution automatique...');
checkWhenReady();

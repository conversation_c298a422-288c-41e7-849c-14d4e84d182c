// Script pour gérer les widgets Toucan et leur taille
document.addEventListener('DOMContentLoaded', function() {
    // Fonction pour ajuster les iframes générées par Toucan
    function adjustToucanIframes() {
        // Sélectionner toutes les iframes
        const iframes = document.querySelectorAll('iframe');

        // Pour chaque iframe, ajuster sa taille
        iframes.forEach(iframe => {
            // Ajouter une classe pour le styling
            iframe.classList.add('toucan-iframe');

            // Forcer la taille à être plus petite
            iframe.style.width = '100%';
            iframe.style.maxWidth = '100%';
            iframe.style.transform = 'scale(0.85)';
            iframe.style.transformOrigin = 'top left';

            // Ajuster la hauteur du conteneur parent si nécessaire
            const parent = iframe.parentElement;
            if (parent) {
                parent.style.overflow = 'hidden';
            }
        });
    }

    // Exécuter la fonction après le chargement de la page
    adjustToucanIframes();

    // Exécuter la fonction à nouveau après un délai pour s'assurer que tous les widgets sont chargés
    setTimeout(adjustToucanIframes, 500);
    setTimeout(adjustToucanIframes, 1000);
    setTimeout(adjustToucanIframes, 2000);

    // Ajuster les iframes lors du redimensionnement de la fenêtre
    window.addEventListener('resize', adjustToucanIframes);

    // Observer les mutations du DOM pour détecter les nouvelles iframes
    const iframeObserver = new MutationObserver((mutations) => {
        let shouldAdjust = false;

        mutations.forEach(mutation => {
            if (mutation.addedNodes.length) {
                mutation.addedNodes.forEach(node => {
                    if (node.tagName === 'IFRAME') {
                        shouldAdjust = true;
                    } else if (node.nodeType === 1 && node.querySelector) {
                        if (node.querySelector('iframe')) {
                            shouldAdjust = true;
                        }
                    }
                });
            }
        });

        if (shouldAdjust) {
            adjustToucanIframes();
        }
    });

    // Observer le document entier
    iframeObserver.observe(document.body, { childList: true, subtree: true });

    // Script original pour créer l'élément embed-938 s'il n'existe pas
    // Fonction pour trouver et mettre à jour l'élément embed-938
    function findAndUpdateEmbed938() {
        // Chercher d'abord l'élément botanic qui contient notre cible
        const botanicElement = document.querySelector('.botanic[data-embed-root="webcomponent"]');

        if (botanicElement) {
            console.log('Élément botanic trouvé');

            // Chercher l'élément embed-938 à l'intérieur
            const embed938 = botanicElement.querySelector('.embed-938');

            if (embed938) {
                // Élément trouvé, mettre à jour son contenu
                embed938.textContent = '938';
                console.log('Élément embed-938 trouvé et mis à jour');
                return true;
            } else {
                // L'élément embed-938 n'existe pas encore, chercher l'élément tile-html__content
                const contentElement = botanicElement.querySelector('.tile-html__content');

                if (contentElement) {
                    console.log('Élément tile-html__content trouvé, création de embed-938');

                    // Créer l'élément embed-938 s'il n'existe pas
                    const newElement = document.createElement('div');
                    newElement.className = 'embed-938';
                    newElement.textContent = '938';

                    // Remplacer le contenu de tile-html__content
                    contentElement.innerHTML = '';
                    contentElement.appendChild(newElement);

                    console.log('Élément embed-938 créé et ajouté');
                    return true;
                }
            }
        }

        // Si on arrive ici, l'élément n'a pas été trouvé
        console.log('Éléments non trouvés, nouvelle tentative dans 1 seconde');
        setTimeout(findAndUpdateEmbed938, 1000);
        return false;
    }

    // Commencer la recherche
    findAndUpdateEmbed938();

    // Observer les mutations du DOM pour détecter quand l'élément botanic est ajouté
    const observer = new MutationObserver((mutations) => {
        mutations.forEach(mutation => {
            if (mutation.addedNodes.length) {
                // Vérifier si un des nouveaux nœuds est notre élément botanic ou le contient
                let shouldCheck = false;

                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) { // Element node
                        if (node.matches && node.matches('.botanic[data-embed-root="webcomponent"]')) {
                            shouldCheck = true;
                        } else if (node.querySelector && node.querySelector('.botanic[data-embed-root="webcomponent"]')) {
                            shouldCheck = true;
                        }
                    }
                });

                if (shouldCheck) {
                    // Attendre un peu que le contenu soit complètement chargé
                    setTimeout(() => {
                        if (findAndUpdateEmbed938()) {
                            // Si l'élément a été trouvé et mis à jour, arrêter l'observation
                            observer.disconnect();
                        }
                    }, 500);
                }
            }
        });
    });

    // Observer le document entier
    observer.observe(document.body, { childList: true, subtree: true });

    // Arrêter l'observation après 15 secondes
    setTimeout(() => {
        observer.disconnect();
    }, 15000);
})

:root {
  --primary-color: #3a7bd5;
  --primary-light: #6fa1ff;
  --primary-dark: #0d47a1;
  --secondary-color: #f5f7fa;
  --text-color: #333;
  --text-light: #666;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#root {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.app-container {
  display: flex;
  height: 100vh;
  background-color: var(--secondary-color);
}

.sidebar {
  width: 380px;
  background-color: #f8fafd;
  padding: 2rem;
  box-shadow: var(--shadow);
  overflow-y: auto;
  height: 100%;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.main-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  height: 100%;
}

h1 {
  color: var(--primary-color);
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  letter-spacing: -0.5px;
  position: relative;
  display: inline-block;
}

h1::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 3px;
}

.todo-form, .todo-edit-form {
  display: flex;
  flex-direction: column;
  padding: 1.8rem;
  gap: 1.2rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-row {
  display: flex;
  width: 100%;
}

.todo-input,
.todo-textarea,
.category-select,
.client-select,
.time-number {
  width: 100%;
  padding: 0.9rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s, box-shadow 0.3s;
  background-color: #fff;
}

.todo-input:focus,
.todo-textarea:focus,
.category-select:focus,
.client-select:focus,
.time-number:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(58, 123, 213, 0.1);
  outline: none;
}

.todo-textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
  line-height: 1.5;
}

.categories {
  display: flex;
  gap: 1.2rem;
  align-items: flex-start;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
}

.form-group label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-color);
  margin-left: 0.2rem;
  display: inline-block;
  margin-bottom: 0.3rem;
  height: 1.2rem; /* Hauteur fixe pour tous les labels */
}

.category-select, .client-select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23555' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
  cursor: pointer;
}

.countdown-row {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: var(--secondary-color);
  padding: 1.2rem;
  border-radius: 6px;
  margin-top: 0.5rem;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.countdown-checkbox {
  transform: scale(1.3);
  accent-color: var(--primary-color);
  margin: 0;
}

.countdown-inputs {
  display: flex;
  gap: 1.2rem;
  margin-top: 0.5rem;
  margin-left: 1.8rem;
}

.time-input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-input label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.time-number {
  width: 70px;
  text-align: center;
  font-weight: 500;
}

.add-button, .save-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s;
  margin-top: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-button:hover, .save-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.cancel-button {
  background-color: var(--danger-color);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cancel-button:hover {
  background-color: #d32f2f;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.edit-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  background-color: white;
  padding: 1.2rem 1.5rem;
  border-radius: 12px;
  box-shadow: var(--shadow);
}

.search-container {
  flex: 1;
  max-width: 450px;
  position: relative;
}

.search-container::before {
  content: '🔍';
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  font-size: 0.9rem;
}

.search-input {
  width: 100%;
  padding: 0.9rem 1rem 0.9rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s;
  background-color: var(--secondary-color);
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.1);
  outline: none;
  background-color: white;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-container label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.95rem;
}

.filter-client-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.client-filter {
  padding: 0.9rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  background-color: var(--secondary-color);
  cursor: pointer;
  min-width: 220px;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23555' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
  transition: all 0.3s;
}

.client-filter:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.1);
  outline: none;
  background-color: white;
}

.client-label-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.3rem;
  height: 1.2rem; /* Même hauteur que les labels normaux */
}

.manage-clients-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
  width: 1.5rem;
  height: 1.2rem;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  margin-left: 0.5rem;
}

.manage-clients-button:hover {
  background-color: rgba(58, 123, 213, 0.1);
  transform: rotate(30deg);
}

.manage-clients-button.filter {
  background-color: var(--secondary-color);
  width: 40px;
  height: 40px;
  border-radius: 8px;
  padding: 0.4rem;
  margin: 0;
}

/* Modal pour la gestion des clients */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.client-manager {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 550px;
  max-width: 90%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

@keyframes slideIn {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.client-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.2rem 1.8rem;
  border-bottom: 1px solid var(--border-color);
  background-color: #f8fafd;
}

.client-manager-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--primary-color);
  font-weight: 600;
  position: relative;
}

.client-manager-header h2::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.8rem;
  cursor: pointer;
  color: var(--text-light);
  transition: all 0.2s;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-button:hover {
  color: var(--danger-color);
  background-color: rgba(244, 67, 54, 0.1);
  transform: rotate(90deg);
}

.client-add-form {
  display: flex;
  padding: 1.5rem 1.8rem;
  gap: 0.8rem;
  border-bottom: 1px solid var(--border-color);
}

.client-input {
  flex: 1;
  padding: 0.9rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s;
  background-color: var(--secondary-color);
}

.client-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.1);
  outline: none;
  background-color: white;
}

.add-client-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 600;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-client-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.client-search {
  padding: 1.5rem 1.8rem;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.client-search::before {
  content: '🔍';
  position: absolute;
  left: 2.8rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  font-size: 0.9rem;
}

.search-client-input {
  width: 100%;
  padding: 0.9rem 1rem 0.9rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s;
  background-color: var(--secondary-color);
}

.search-client-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.1);
  outline: none;
  background-color: white;
}

.client-list {
  padding: 1.5rem 1.8rem;
  overflow-y: auto;
  max-height: 50vh;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.client-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.2rem;
  border-radius: 8px;
  background-color: var(--secondary-color);
  transition: all 0.2s;
  border: 1px solid transparent;
}

.client-item:hover {
  background-color: #e8eef7;
  border-color: var(--border-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.client-item span {
  font-weight: 500;
  color: var(--text-color);
}

.remove-client-button {
  background: none;
  border: none;
  font-size: 1.3rem;
  cursor: pointer;
  color: var(--danger-color);
  opacity: 0.6;
  transition: all 0.2s;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.remove-client-button:hover {
  opacity: 1;
  background-color: rgba(244, 67, 54, 0.1);
  transform: scale(1.1);
}

.remove-client-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
  background-color: transparent;
  transform: none;
}

.no-clients {
  text-align: center;
  color: var(--text-light);
  font-style: italic;
  padding: 3rem 0;
  font-size: 1.1rem;
}

.todo-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.todo-item {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 8px;
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
  position: relative;
}

.todo-item.completed {
  opacity: 0.8;
  border-left: 4px solid var(--success-color);
}

.todo-item.active {
  border: 1px solid var(--primary-color);
  box-shadow: 0 0 0 2px rgba(58, 123, 213, 0.2), var(--shadow);
  transform: translateY(-2px);
  z-index: 10;
  margin-bottom: 1.5rem; /* Espace supplémentaire en dessous pour séparer des autres tâches */
}

.todo-item.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: var(--primary-color);
}

/* Badge "En cours" pour la tâche active */
.todo-item.active::after {
  content: 'EN COURS';
  position: absolute;
  top: 1px;
  right: 1px;
  background-color: var(--primary-color);
  color: white;
  font-size: 0.5em;
  font-weight: bold;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.todo-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.todo-item-header:hover {
  background-color: var(--secondary-color);
}

.todo-item.active .todo-item-header {
  background-color: rgba(58, 123, 213, 0.05);
}

.todo-header-left {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  flex: 1;
}

.expand-icon {
  font-size: 0.8rem;
  color: var(--text-light);
  transition: transform 0.3s;
}

.expand-icon.expanded {
  transform: rotate(0deg);
}

.todo-header-info {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.todo-title-timer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 0.3rem;
}

.todo-text {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-color);
  margin-right: 1rem;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.client-prefix {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.title-separator {
  color: #aaa;
  margin: 0 0.3rem;
}

.task-title {
  font-weight: 500;
}

.inline-timer {
  font-family: monospace;
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
  background-color: var(--secondary-color);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.timer-active-indicator {
  width: 8px;
  height: 8px;
  background-color: var(--success-color);
  border-radius: 50%;
  display: inline-block;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.5;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

.todo-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
  color: var(--text-light);
}

.todo-item input[type="checkbox"] {
  margin-right: 0;
  transform: scale(1.2);
}

.todo-category {
  font-size: 0.7rem;
  padding: 0.1rem 0.5rem;
  border-radius: 12px;
  color: white;
  font-weight: 500;
  text-transform: uppercase;
}

.todo-category.email {
  background-color: var(--primary-color);
}

.todo-category.questionnaire {
  background-color: var(--warning-color);
}

.todo-category.investigation {
  background-color: var(--danger-color);
}

.todo-client {
  font-size: 0.7rem;
  padding: 0.1rem 0.5rem;
  border-radius: 12px;
  background-color: #607D8B;
  color: white;
  font-weight: 500;
}

.todo-date {
  font-size: 0.7rem;
  color: var(--text-light);
}

.todo-date.completed {
  color: var(--success-color);
}

.todo-header-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  padding: 0.3rem;
  border-radius: 4px;
  transition: all 0.2s;
  opacity: 0.7;
  margin-left: 0.2rem;
}

.action-button:hover {
  background-color: var(--secondary-color);
  opacity: 1;
}

.action-button:disabled {
  cursor: default;
  opacity: 1;
}

.switch-button {
  font-size: 1.2rem;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.switch-button:hover:not(:disabled) {
  background-color: rgba(58, 123, 213, 0.1);
  transform: scale(1.1);
}

.switch-button.active {
  background-color: rgba(76, 175, 80, 0.1);
  opacity: 1;
}

.todo-item-body {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--secondary-color);
}

.todo-description {
  margin-bottom: 1.5rem;
  background-color: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.todo-description.empty {
  background-color: #f9f9f9;
  border: 1px dashed var(--border-color);
  text-align: center;
  color: var(--text-light);
  font-style: italic;
}

.todo-description h4 {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.description-content {
  font-size: 0.95rem;
  color: var(--text-color);
  margin: 0;
  line-height: 1.6;
  white-space: pre-line;
}

.todo-description p {
  font-size: 0.9rem;
  margin: 0;
}

.todo-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.todo-category {
  font-size: 0.8rem;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  color: white;
  font-weight: 500;
}

.todo-category.email {
  background-color: #2196F3; /* Bleu */
}

.todo-category.questionnaire {
  background-color: #FF9800; /* Orange */
}

.todo-category.investigation {
  background-color: #9C27B0; /* Violet */
}

.todo-client {
  font-size: 0.8rem;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  background-color: #607D8B; /* Gris-bleu */
  color: white;
  font-weight: 500;
}

.counter-container {
  display: flex;
  align-items: center;
  margin-right: 1rem;
  background-color: #f0f0f0;
  border-radius: 4px;
  padding: 0.2rem;
}

.counter-button {
  background-color: #4caf50;
  color: white;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

.counter-button.decrement {
  background-color: #ff9800;
}

.counter-button:hover {
  background-color: #45a049;
}

.counter-button.decrement:hover {
  background-color: #f57c00;
}

.counter-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.counter-value {
  margin: 0 0.5rem;
  font-weight: bold;
  min-width: 1.5rem;
  text-align: center;
}

.delete-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 0.5rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.delete-button:hover {
  background-color: #d32f2f;
}

.timer-container {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 8px;
  padding: 1.2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.timer-display {
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 1rem;
  font-family: monospace;
  color: var(--text-color);
  letter-spacing: 2px;
}

.timer-mode {
  font-size: 0.9rem;
  font-weight: normal;
  color: var(--text-light);
  display: block;
  margin-bottom: 0.5rem;
}

.timer-display.countdown {
  color: var(--primary-color);
}

.timer-display.countdown.warning {
  color: var(--danger-color);
}

.progress-bar {
  height: 6px;
  background-color: var(--secondary-color);
  border-radius: 3px;
  margin-bottom: 1.2rem;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.timer-controls {
  display: flex;
  justify-content: center;
  gap: 0.8rem;
}

.timer-button {
  padding: 0.7rem 1.2rem;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.timer-button.start {
  background-color: var(--primary-color);
  color: white;
}

.timer-button.start:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
}

.timer-button.pause {
  background-color: var(--warning-color);
  color: white;
}

.timer-button.pause:hover {
  background-color: #e08600;
  transform: translateY(-1px);
}

.timer-button.reset {
  background-color: var(--danger-color);
  color: white;
}

.timer-button.reset:hover {
  background-color: #d32f2f;
  transform: translateY(-1px);
}

.timer-button:disabled {
  background-color: #e0e0e0;
  color: #999;
  cursor: not-allowed;
  transform: none;
}

.empty-message {
  text-align: center;
  color: #888;
  font-style: italic;
  padding: 1rem;
}

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Company Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
    <script src="https://unpkg.com/@dotlottie/player-component@2.7.12/dist/dotlottie-player.mjs" type="module"></script>

    <style>
        body {
            height: 100vh;
            overflow-y: auto;
        }
        .dashboard-container {
            max-height: calc(100vh - 2rem);
            overflow-y: auto;
        }
        canvas {
            max-height: 150px !important;
        }
    </style>
</head>
<body class="bg-gray-50 p-6">
    <div class="dashboard-container max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-2">
                <div class="bg-black p-2 rounded">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                    </svg>
                </div>
                <h1 class="text-xl font-bold">Enquête de Satisfaction Globale</h1>
            </div>
            <div class="flex items-center space-x-2">
                <button class="flex items-center space-x-1 bg-white px-4 py-2 rounded-lg shadow">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    <span>Trend: 7 days</span>
                </button>
            </div>
        </div>

        <!-- Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <!-- Messages -->
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="flex justify-between items-center mb-4">
                    <div class="flex items-center space-x-2">
                        <div class="bg-blue-100 p-2 rounded">
                            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"/>
                            </svg>
                        </div>
                        <span class="text-gray-600">Messages</span>
                    </div>
                    <span class="text-green-500">+700</span>
                </div>
                <div class="text-2xl font-bold mb-2">4,346</div>
                <div class=" bg-green-100 p-6 rounded-md shadow-md">
                    Ici l'emplacement pour l'embed
                </div>
            </div>

            <!-- Feedback -->
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="flex justify-between items-center mb-4">
                    <div class="flex items-center space-x-2">
                        <div class="bg-green-100 p-2 rounded">
                            <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm9.707 5.707a1 1 0 00-1.414-1.414L9 12.586l-1.293-1.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <span class="text-gray-600">Feedback</span>
                    </div>
                    <span class="text-green-500">+1,466</span>
                </div>
                <div class="text-2xl font-bold mb-2">1.0% NPS</div>
                 <div class=" bg-green-100 p-6 rounded-md shadow-md">
                    Ici l'emplacement pour l'embed
                </div>
            </div>

            <!-- Response Rate -->
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="flex justify-between items-center mb-4">
                    <div class="flex items-center space-x-2">
                        <div class="bg-purple-100 p-2 rounded">
                            <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"/>
                                <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"/>
                            </svg>
                        </div>
                        <span class="text-gray-600">Ratio</span>
                    </div>
                    <span class="text-green-500">+11,722.4%</span>
                </div>
                <div class="text-2xl font-bold mb-2">55.8%</div>
                <div class="h-32">
                    <div class=" bg-green-100 p-6 rounded-md shadow-md">
                    Ici l'emplacement pour l'embed
                </div>
                </div>
            </div>

            <!-- Rising Stars -->
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="flex justify-between items-center mb-4">
                    <div class="flex items-center space-x-2">
                        <div class="bg-yellow-100 p-2 rounded">
                            
<dotlottie-player src="https://lottie.host/875107d2-f425-462b-a095-0b6c950faa52/V6lcogEhrl.lottie" background="transparent" speed="1" style="width: 45px; height: 45px" loop autoplay></dotlottie-player>
                        </div>
                        <span class="text-gray-600 font-bold text-sm ">Objectifs atteint</span>
                    </div>
                </div>
                <div class="space-y-4"> 
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2"> 
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQkLyDYvprnX-IKNfafzhjdYi6G8EjENKNDHg&s" class="w-5 h-5 rounded-full" alt="User">
                            <span>Fabien H.</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span>669</span>
                            <span class="text-green-500">+668</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQkLyDYvprnX-IKNfafzhjdYi6G8EjENKNDHg&s" class="w-5 h-5 rounded-full" alt="User">
                            <span>Cédric</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span>519</span>
                            <span class="text-green-500">+518</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQkLyDYvprnX-IKNfafzhjdYi6G8EjENKNDHg&s" class="w-5 h-5 rounded-full" alt="User">
                            <span>Elon Musk</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span>142</span>
                            <span class="text-green-500">+2</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- CSAT -->
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <div class="text-2xl font-bold">7.11</div>
                        <div class="text-sm text-gray-600">CSAT</div>
                    </div>
                    <span class="text-green-500">+18.5%</span>
                </div>
            </div>

            <!-- Sentiment -->
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="mb-2">Sentiments</div>
                <div class="text-sm text-gray-600">Analyse d'expériences</div>
                <div class="mt-2 bg-gray-200 rounded-full h-2">
                    <div class="bg-orange-500 h-2 rounded-full" style="width: 45%"></div>
                </div>
                <div class="mt-2">Neutre</div>
            </div>

            <!-- Engagement -->
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="mb-2">Engagement</div>
                <div class="mt-2 bg-gray-200 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full" style="width: 88%"></div>
                </div>
                <div class="mt-2 flex justify-between">
                    <span>88 sur 100 points</span>
                    <span class="text-green-500">Très haut</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuration des graphiques avec des dimensions fixes
        const messagesCtx = document.getElementById('messagesChart').getContext('2d');
        const responseCtx = document.getElementById('responseChart').getContext('2d');

        // Graphique des messages
        new Chart(messagesCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    data: [4000, 4200, 4100, 4300, 4200, 4346],
                    borderColor: '#3B82F6',
                    tension: 0.4,
                    borderWidth: 2,
                    pointRadius: 0
                }]
            },
            options: {
                plugins: { legend: { display: false } },
                scales: {
                    x: { display: false },
                    y: { display: false }
                },
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Graphique du taux de réponse
        new Chart(responseCtx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [55.8,55.8],
                    backgroundColor: ['#3B82F6', '#E5E7EB'],
                    borderWidth: 0
                }]
            },
            options: {
                plugins: { legend: { display: false } },
                cutout: '80%',
                responsive: true,
                maintainAspectRatio: false
            }
        });
    </script>
</body>
</html>
* {
    outline: none !important;
}

.introTitle

/*text d'introduction*/
    {
    background-color: #00634C;
    color: #FFF !important;
    padding: 1.25rem;
    border-radius: 6px;
}

img.sg-header-image

/*image header*/
    {
    max-width: 100% !important;
    margin: auto;
    display: block;
}





.sg-max-characters {
    display: none;
}

.colorGreen {
    color: #00634C;
    font-weight: bold;
}

/*Mettre en blanc les fonds des questions tableau*/
.sg-type-table .sg-table .sg-even-row td,
.sg-type-table .sg-table .sg-even-row th {
    background-color: #fff !important;
}

/*Fin Mettre en blanc les fonds des questions tableau*/



/*CSS MAJ*/
.sg-survey-form {
    max-width: 900px;
    width: 90%
}


/* box shadow pour chaque question */
fieldset.sg-question.sg-type-radio,
fieldset.sg-type-table,
.sg-type-essay {
    width: 100%;
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    padding-top: 25px;
    padding-left: 15px;
    padding-right: 15px;
    border-radius: 15px;
    position: relative
}

fieldset.sg-question.sg-type-radio legend,
fieldset.sg-type-table legend {
    display: contents
}

.sg-survey-form {
    width: 90%
}

.sg-rating-scale thead {
    display: none
}

tr.sg-rating-set .sg-first-cell,
tr.sg-rating-set .sg-last-cell {
    width: auto !important;
    position: absolute
}


tr.sg-rating-set {
    margin-top: 35px
}

.sg-rating-scale {
    position: relative
}


/* mettre le conteneur en display flex et aligner les td dedans */
.sg-rating-scale tbody .sg-rating-set,
.sg-table tbody .sg-odd-row {
    list-style-type: none;
    counter-reset: css-counter -1;
    display: flex !important;
    justify-content: center;
    align-items: center;
    gap: 1px
}

/* rupture du flex en mobile */

/* mettre les td en display block avec un hauteur de 60px */
.sg-rating-scale tbody .sg-rating-set td,
.sg-table tbody .sg-odd-row td {
    display: block !important;
    padding: 0 !important;
}

td.sg-cell.sg-cell-1.sg-first-cell.sg-cell-label.sg-cell-left-label

/*text à gauche*/
    {
    color: #EE3C32;
    height: fit-content!important;
    top:5px;
    left: 5px;

}

td.sg-cell.sg-cell-13.sg-last-cell.sg-cell-label.sg-cell-right-label,
td.sg-cell.sg-cell-14.sg-last-cell.sg-cell-label.sg-cell-right-label

/*text à droite*/
    {
    color: #7EBC35;
    height: fit-content!important;
    top:5px;
    right: 5px;
}



/* mettre les chiffre dans les span */
.sg-rating-scale tbody .sg-rating-set span.sg-icon-target {
    counter-increment: css-counter 1;
}


.sg-type-radio .sg-rating-scale-15 .sg-cell {
    width: auto
}

/* c'est l'aspect des td en pseudo elements la couleur des textes en noir et les chiffres dedans*/
.sg-replace-icons .sg-rating-scale tbody input[type="radio"]+label span.sg-icon-target:before {
    width: 70px;
    content: counter(css-counter);
    padding: 23px 0 ;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: inherit;
    color: black;
    cursor: pointer
}






.sg-replace-icons .sg-rating-scale tbody  input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody  input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 1;
    box-shadow: rgb(0 49 52 / 25%) 0px 5px 16px;
    background-color: rgb(243, 245, 243);
    border-radius: 5px;
    color: black !important;
    transform: scale(1.1)!important;

}


/* pour l'option table */

.sg-table tbody .sg-even-row,
.sg-table tbody .sg-odd-row {
    list-style-type: none;
    counter-reset: css-counter -1;
    display: flex !important;
    gap: 1px;
    justify-content: start;
    align-items: center !important
}

.sg-table tbody .sg-even-row td,
.sg-table tbody .sg-odd-row td {
    padding: 0 !important;
    height: auto !important;
    margin-top: 10px;
}




.sg-table thead {
    display: none;
}

.sg-table tbody .sg-even-row .sg-first-cell,
.sg-table tbody .sg-odd-row .sg-first-cell {
    width: 261px;
}


/* mettre les chiffre dans les span */
.sg-table tbody .sg-odd-row td span.sg-icon-target,
.sg-table tbody .sg-even-row td span.sg-icon-target {
    counter-increment: css-counter 1;

}






.sg-max-characters {
    display: none;
}

.colorGreen {
    color: #00634C;
    font-weight: bold;
}

/*Mettre en blanc les fonds des questions tableau*/
.sg-type-table .sg-table .sg-even-row td,
.sg-type-table .sg-table .sg-even-row th {
    background-color: #fff !important;
}

/*Fin Mettre en blanc les fonds des questions tableau*/



/*CSS MAJ*/
.sg-survey-form {
    max-width: 900px;
    width: 90%
}




fieldset.sg-question.sg-type-radio legend,
fieldset.sg-type-table legend {
    display: contents
}

.sg-survey-form {
    width: 90%
}

.sg-rating-scale thead {
    display: none
}



.sg-rating-scale {

    position: relative
}




/* couleur incrémenté par chiffre GLE*/
.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(2) input[type="radio"]+label span.sg-icon-target::before {
    background: #bd0000;
    border-radius: 10px 0 0 10px;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(3) input[type="radio"]+label span.sg-icon-target::before {
    background: #cf0000;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(4) input[type="radio"]+label span.sg-icon-target::before {
    background: #e62300;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(5) input[type="radio"]+label span.sg-icon-target::before {
    background: #e13600;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(6) input[type="radio"]+label span.sg-icon-target::before {
    background: #e55300;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(7) input[type="radio"]+label span.sg-icon-target::before {
    background: #e57c00;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(8) input[type="radio"]+label span.sg-icon-target::before {
    background: #e5a600;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(9) input[type="radio"]+label span.sg-icon-target::before {
    background: #8cb026;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(10) input[type="radio"]+label span.sg-icon-target::before {
    background: #7d9e22;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(11) input[type="radio"]+label span.sg-icon-target::before {
    background: #568528;
}

.sg-replace-icons .sg-rating-scale tbody tr td:nth-child(12) input[type="radio"]+label span.sg-icon-target::before {
    background: #207716;
    border-radius: 0 10px 10px 0;
}







/* question tableau td non concerné */
.tableau--question .sg-table tbody td:nth-child(13) input[type="radio"]+label span.sg-icon-target:before {
    padding: 5px 5px  !important;
    margin-left: 5px;
    border-radius: 5px;
    outline: 2px dotted rgb(243, 243, 243);
    content:"Je ne sais pas";
    width: fit-content;
    height: 40px;
}



/* c'est l'aspect des td en pseudo elements la couleur des textes en noir et les chiffres dedans*/
.tableau--question .sg-table tbody input[type="radio"]+label span.sg-icon-target:before {
    width: 40px;
    height: 40px;
    content: counter(css-counter);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: inherit;
    color: black;
    cursor: pointer;
    font-size: 12px;
    font-weight: 800;

}


/* les td en hover question tableau */
.tableau--question tbody  input[type="radio"]:checked+label span.sg-icon-target:before,
.tableau--question tbody  input[type="radio"]:hover+label span.sg-icon-target:before {
    
    transform: scale(1.1)!important;
    color: yellow;

}


/* question tableau td non concerné hover et checked */
.tableau--question .sg-table tbody td:nth-child(13) input[type="radio"]:checked+label span.sg-icon-target:before,.tableau--question .sg-table tbody td:nth-child(13) input[type="radio"]:hover+label span.sg-icon-target:before {
   transform: scale(1)!important;
    color: black;
    box-shadow: none;
    outline: 2px dotted rgb(138, 226, 7);
    background-color: #00cf00
}


/* couleur incrémenté par chiffre */
.tableau--question .sg-table tbody tr td:nth-child(2) input[type="radio"]+label span.sg-icon-target::before {
    background: #bd0000;
    border-radius: 10px 0 0 10px;
}

.tableau--question .sg-table tbody tr td:nth-child(3) input[type="radio"]+label span.sg-icon-target::before {
    background: #cf0000;
}

.tableau--question .sg-table tbody tr td:nth-child(4) input[type="radio"]+label span.sg-icon-target::before {
    background: #e62300;
}

.tableau--question .sg-table tbody tr td:nth-child(5) input[type="radio"]+label span.sg-icon-target::before {
    background: #e13600;
}

.tableau--question .sg-table tbody tr td:nth-child(6) input[type="radio"]+label span.sg-icon-target::before {
    background: #e55300;
}

.tableau--question .sg-table tbody tr td:nth-child(7) input[type="radio"]+label span.sg-icon-target::before {
    background: #e57c00;
}

.tableau--question .sg-table tbody tr td:nth-child(8) input[type="radio"]+label span.sg-icon-target::before {
    background: #e5a600;
}

.tableau--question .sg-table tbody tr td:nth-child(9) input[type="radio"]+label span.sg-icon-target::before {
    background: #8cb026;
}

.tableau--question .sg-table tbody tr td:nth-child(10) input[type="radio"]+label span.sg-icon-target::before {
    background: #7d9e22;
}

.tableau--question .sg-table tbody tr td:nth-child(11) input[type="radio"]+label span.sg-icon-target::before {
    background: #568528;
}

.tableau--question .sg-table tbody tr td:nth-child(12) input[type="radio"]+label span.sg-icon-target::before {
    background: #207716;
    border-radius: 0 10px 10px 0;
}
/* fin couleur td tableau */


/* hover et cheked pour les td GLE */
.sg-replace-icons .sg-rating-scale tbody input[type="radio"]:checked+label span.sg-icon-target:before,
.sg-replace-icons .sg-rating-scale tbody input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    transform: scale(1.1);
    color: yellow
}



/* hover pour les éléments de la table */
.sg-table tbody tr td input[type="radio"]:checked+label span.sg-icon-target::before,
.sg-table tbody tr td input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 0.8;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    content: counter(css-counter);
}



/* mobile */
@media screen and (max-width:900px) {
    * {
        outline: none !important;
    }
    .sg-content {
        padding: 0;
    }

    .sg-question-set {
        width: 90vw;
        margin: auto;
    }

    .sg-replace-icons .sg-rating-scale tbody input[type="radio"]+label span.sg-icon-target:before {
        width: 8vw !important;
        height: auto !important;
        font-size: 12px;
    }

    .sg-type-radio-likert .sg-question-options {
        overflow: hidden;
    }

    fieldset.sg-question.sg-type-radio,
    fieldset.sg-type-table,
    .sg-type-essay {
        width: 100%;
        box-shadow: none;
        padding-top: 0;
        padding-left: 0;
        padding-right: 0;
        border-radius: 0;
        position: relative;
    }

    .sg-question-options {
        padding: 0;
        margin: 0;
    }

    .tableau--question .sg-first-cell {
        /* labelle en mobile  */
        width: fit-content!important;
    }

    td.sg-cell.sg-cell-1.sg-first-cell.sg-cell-label.sg-cell-left-label

    /*text à gauche*/
        {
        height: fit-content !important;
    }

    td.sg-cell.sg-cell-13.sg-last-cell.sg-cell-label.sg-cell-right-label,
    td.sg-cell.sg-cell-14.sg-last-cell.sg-cell-label.sg-cell-right-label

    /*text à droite*/
        {
        height: fit-content !important;
    }

    .nonConcerned .sg-cell-13
    {
        position: absolute;
        top: 87px;
        right: 5px;
    }

  

    .tableau--question tr .sg-last-cell input[type="radio"]+label span.sg-icon-target::before {
        border: 1px solid #00634C;
    }

    .nonConcerned .sg-rating-set {
        padding-bottom: 46px;
    }

    .nonConcerned .sg-rating-scale tbody tr td:nth-child(13) input[type="radio"]+label span.sg-icon-target::before {
        padding: 5px 50px;
        border: 1px solid #00634C;
    }



    /* tableau question */
    .tableau--question tr {
        height: 173px;
        position: relative;
        overflow: hidden;
        padding-left:15px
    }

    .tableau--question tr th {
        position: absolute;
        top: 11px
    }

    .sg-table tbody tr td:nth-child(13) input[type="radio"]:hover+label span.sg-icon-target:before {
        border: none;
    }

    .sg-table tbody input[type="radio"]+label span.sg-icon-target:before {
        width: 8vw!important;
    }
    .tableau--question tbody tr td:nth-child(13) input[type="radio"]+label span.sg-icon-target::before{
        position: absolute;
        left: 16px;
        bottom: 5px;
        width: fit-content!important;
    }

    /* 5 review */
    .tr-5-rating tr.sg-rating-set .sg-first-cell, .tr-5-rating tr.sg-rating-set .sg-last-cell {
       font-size: 10px;
        
    }
    .tr-5-rating td.sg-cell.sg-cell-1.sg-first-cell.sg-cell-label.sg-cell-left-label {
        font-size: 10px;
    }



    /* spécifique */

    .tableau--question .row-270{
        height: 190px;
    }
  
    .nonConcerned .sg-rating-scale tbody tr td:nth-child(13) input[type="radio"]+label span.sg-icon-target::before {
        padding: 5px 50px;
        border: 1px solid #00634C;
        content: "Non concerné(e)" !important;
        position: absolute;
        top: 15px;
        right: 0px;
    }



}


.sg-table tbody tr.sg-hide{
  display:none!important
}



/* spécifique pour le non concerné */
.nonConcerned .sg-replace-icons .sg-rating-scale tbody tr td:nth-child(13) input[type="radio"]+label span.sg-icon-target::before {
    background: #FFF;
    margin-left: 5px;
    padding: 0 10px;
    content: 'Non concerné(e)';
    width: auto;
    border: 1px solid rgb(223, 223, 223);
    font-size: 13px;
}


.nonConcerned tbody tr td:nth-child(13) input[type="radio"]:checked+label span.sg-icon-target:before,
.nonConcerned tbody tr td:nth-child(13) input[type="radio"]:hover+label span.sg-icon-target:before {
    opacity: 1;
    box-shadow: rgb(0 49 52 / 25%) 0px 5px 16px;
    background-color: rgb(243, 245, 243);
    border-radius: 5px;
    outline: 2px dotted green !important;
    font-size: 13px !important;
    color: black !important;
    transform: scale(1)!important;

}

/* pour le rating à 5 étoiles */

.tr-5-rating tr{
    justify-content: center;
    gap: 1;
}

.tr-5-rating tr td {
    margin: 0!important;
    width: auto!important;
}
.tr-5-rating tr.sg-rating-set .sg-first-cell, .tr-5-rating tr.sg-rating-set .sg-last-cell {
    width: auto !important;
    position: relative!important;
    margin-left: 10px!important;
    
}
.tr-5-rating td.sg-cell.sg-cell-1.sg-first-cell.sg-cell-label.sg-cell-left-label {
    color: #EE3C32;
    height: fit-content!important;
    position: relative!important;
    margin-right: 10px!important;
}


.tr-5-rating tbody tr td:nth-child(2) input[type="radio"]+label span.sg-icon-target::before {
    background: #ce0f0f!important;
    border-radius: 10px 0 0 10px;
}


.tr-5-rating tbody tr td:nth-child(3) input[type="radio"]+label span.sg-icon-target::before {
    background: #ce650f!important;
}
.tr-5-rating tbody tr td:nth-child(4) input[type="radio"]+label span.sg-icon-target::before {
    background: #eed600!important;
}
.tr-5-rating tbody tr td:nth-child(5) input[type="radio"]+label span.sg-icon-target::before {
    background: #207716!important;
}

.tr-5-rating tbody tr td:nth-child(6) input[type="radio"]+label span.sg-icon-target::before {
    background: #239b15!important;
    border-radius: 0 10px 10px 0;

}

/* pour le tableau normal */
.tableau--normal .sg-table tbody td:nth-child(13) input[type="radio"]+label span.sg-icon-target:before {
    padding: 5px 5px  !important;
    margin-left: 5px;
    border-radius: 5px;
    outline: 2px dotted rgb(243, 243, 243);
    content:"Je ne sais pas";
    width: fit-content;
    height: 40px;
}



/* c'est l'aspect des td en pseudo elements la couleur des textes en noir et les chiffres dedans*/
.tableau--normal .sg-table tbody input[type="radio"]+label span.sg-icon-target:before {
    width: 40px;
    height: 40px;
    content: counter(css-counter);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: inherit;
    color: black;
    cursor: pointer;
    font-size: 12px;
    font-weight: 800;

}


/* les td en hover question tableau */
.tableau--normal tbody  input[type="radio"]:checked+label span.sg-icon-target:before,
.tableau--normal tbody  input[type="radio"]:hover+label span.sg-icon-target:before {
    
    transform: scale(1.1)!important;
    color: yellow;

}


/* question tableau td non concerné hover et checked */
.tableau--normal .sg-table tbody td:nth-child(13) input[type="radio"]:checked+label span.sg-icon-target:before,.tableau--normal .sg-table tbody td:nth-child(13) input[type="radio"]:hover+label span.sg-icon-target:before {
   transform: scale(1)!important;
    color: black;
    box-shadow: none;
    outline: 2px dotted rgb(138, 226, 7);
    background-color: #00cf00
}


/* couleur incrémenté par chiffre */
.tableau--normal .sg-table tbody tr td:nth-child(2) input[type="radio"]+label span.sg-icon-target::before {
    background: #00cf00;
    border-radius: 10px 0 0 10px;
    content: "Oui";
}

.tableau--normal .sg-table tbody tr td:nth-child(3) input[type="radio"]+label span.sg-icon-target::before {
    background: #cfa900;
    content: "Non";
    border-radius: 0 10px 10px 0;
}


/* fin couleur td tableau */

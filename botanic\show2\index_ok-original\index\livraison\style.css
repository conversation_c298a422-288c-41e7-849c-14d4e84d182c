* {
  font-family: "Dosis", serif;
  font-optical-sizing: auto;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  color: #00564B;

}

h2 {
  font-size: 2rem;
  text-transform: uppercase;
}

h3 {
  font-size: 1.5rem;
}

.grillePrincipale {
  width: 97vw;
  height: 98vh;
  margin: auto;
  display: flex;
  justify-content: space-between;
  gap: 15px;
  overflow-y: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-top: 35px !important;
  margin-bottom: 25px !important;
  margin-bottom: 15px;
  width: 97vw;
  margin: auto;

}

.grand-titre {
  font-size: 2.7em;
}

.titre-moyen {
  font-size: 1.5rem;
  font-weight: bold;
}

.petit-titre {
  font-size: 1.1rem;
  font-weight: bold;
}



.col {
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 10px;
}

.card {
  display: flex;
  flex-direction: column;
  border-radius: 15px;
  overflow: hidden;
  border: 1px solid rgb(147, 190, 147);
  height: 320px;
  min-height: 370px;
}

.card.botanic{
  min-height: 370px;
}

.card.service{
  min-height: 150px;
}
.entete {
  background-color: #00564B;
  display: flex;
  justify-content: space-between;
  height: 77px;
  padding: 10px 15px;
}

.iconCo {
  display: flex;
  align-items: center;
  gap: 8px;

}

.iconCo img {
  width: 35px;
  height: 35px;
}

.iconCo p {
  color: white;
  font-size: 1rem;
  display: flex;
  flex-direction: column;
  line-height: 20px;
  font-weight: bold;
  text-transform: uppercase;
}

.iconCo p span {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;

}

.repondant {
  color: white;
  font-size: 1.5rem;
  display: flex;
  flex-direction: column;
  line-height: 25px;
  font-weight: bold;
  text-transform: uppercase;
  border-left: 1px solid rgb(62, 109, 93);
  padding-left: 25px;
  padding-right: 35px;

}

.repondant span {
  color: white;
  font-size: 1rem;
  font-weight: bold;
}

.score {
  color: #00564B;
}



/* chartservie magasin */
tctc-embed[id*="873f13db-532d-4a95-b846-40b14f6af2fa"]{
  height:210px;
  margin-top:21px
}


/* repondant magasin */
tctc-embed[id*="5f0b9267-af40-4ac4-9bc5-fd8ef8a62a2c"] {
  position:relative;
  height: 25px;
 }



 /* score magasin */
tctc-embed[id*="c9b966b7-737b-488f-b0f5-4471d3e42a81"] {
  position:relative;
background-color: transparent;
  top:-25px;
  z-index: -1000;
 }


 


/* chart botanic */
tctc-embed[id*="a1f4923e-5827-4b4b-a735-a2f2c44dcedd"]{
  height:210px;
  margin-top:21px
}

/* repondant botanic */
tctc-embed[id*="268138d0-5bd8-4ef9-9e23-32615087bea6"]{
  height:25px;
}

tctc-embed[id*="253c110d-f2cb-07e7-b053-3782f6ee27a6"]{
overflow: visible!important;
}




/* score botanic */
tctc-embed[id*="66fc0b66-d25e-47f2-9efe-129d34d6e19d"] {
  position:relative;
background-color: transparent;
top:-25px;
z-index: -1000;
 }




/* chart service client */
.service_client tctc-embed[id*="a1f4923e-5827-4b4b-a735-a2f2c44dcedd"]{
  height:180px;
  margin-top:21px
}


/* score service client */
.service_client tctc-embed[id*="66fc0b66-d25e-47f2-9efe-129d34d6e19d"] {
  position:relative;
background-color: transparent;
  top:-5px;
  z-index: -1000;
 }




/* carte jardin */
tctc-embed[id*="d1936807-11d4-4d13-a5d7-21812b2d563a"]{
  position:relative;
  top:-13px
}

/* carte marché bio */
tctc-embed[id*="636cc5df-5c0b-4599-bc20-5ca826e92bac"]{
  position:relative;
  top:-13px
}


/* carte maison bio */
tctc-embed[id*="8ca843d6-a5e3-4ac2-badf-05e0fba7deed"]{
  position:relative;
  top:-13px
}



/* carte rapport qualité prix */
tctc-embed[id*="39607b4c-2127-455e-922d-bd033e7d18f2"]{
  position:relative;
  top:-13px
}



/* carte animalier */
tctc-embed[id*="efaef356-8d75-47d5-8b61-ff961f06b25d"]{
  position:relative;
  top:-13px
}



/* carte disponibilité */
tctc-embed[id*="cccc9a61-71f5-4ad9-b0e8-e155cf37983e"]{
  position:relative;
  top:-13px
}

/* les scores en bas des cahrts */
/* pour magasin */
tctc-embed[id*="d15245ba-dabe-9243-777c-6a0b28263361"]{
  height: 50px;
  width: 100%;
  color:white
}




.embed {
  width: 100%;
  /* max-height: 150px; */
  margin: auto;
 height: 300px;
}

.score {
  font-size: 2.7rem;
  font-weight: bold;
}

.score span {
  font-size: 1rem;
}



/* widget */
.conteneur {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.widget-container {
  display: flex;
  gap: 4px;
}

.widget {
  display: flex;
  width: 100%;
  border-radius: 15px;
  overflow: hidden;
  border: 1px solid rgb(147, 190, 147);
  height: 87px;
}

.w-icon {
  background-color: #00564B;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 50%;
}

.w-icon img {
  width: 35px;
  height: 35px;
}

.w-icon p {
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  text-align: center;
}


.score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.w-score {
  font-size: 2.7rem;
  font-weight: bold;
}

.w-score span {
  font-size: 1rem;
}


.w-repondant {
  color: #00564B;
  font-size: 1.2rem;
  line-height: 25px;
  font-weight: bold;
  text-transform: uppercase;

}

.w-repondant span {
  color: #00564B;
  font-size: 0.8rem;
  font-weight: bold;
  margin-left: 5px;
}


/* 3e colonne */
.thirdCol {
  background-color: rgba(255, 228, 196, 0.212);
  border-radius: 15px;
  position: relative;
  padding-top: 25px;
  margin-top: 0;
}

.couple {
  position: absolute;
  width: 50vh;
  bottom: 1vh;
}

.green-card-container-boy {
  position: absolute;
  bottom: 57vh;
  left: 2vw;
}

.green-card-boy,
.green-card-girl {
  height: fit-content !important;
}

.green-card-boy {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20px;
  width: 15vw;
  height: fit-content;
  background-color: #8bb026;
  border-radius: 15px;
  padding-bottom: 35px;
  padding-top: 25px;
}

.green-card-boy p,
.green-card-girl p {
  color: white;
  font-weight: 700;
  z-index: 500;
}

.green-card-boy .ratings,
.green-card-boy .ratings .score,
.green-card-boy .ratings .score span {
  color: white;
}

.green-card-girl .ratings,
.green-card-girl .ratings .score,
.green-card-girl .ratings .score span {
  color: white;
}

.ratings .score {
  font-size: 1.2rem;
  display: flex;
  align-items: baseline;
}

.ratings .score div {
  margin-left: 15px;
}

.ratings .score img {
  width: 15px;
}

.ratings .score span {
  font-size: 0.8rem;
}

.ratings {
  position: absolute;
  bottom: 5px;
  right: 20px
}


.cursor-boy {
  position: absolute;
  bottom: -5px;
  left: 30px;
  width: 2vh;
  height: 2vh;
  background-color: #8bb026;
  transform: rotate(-40deg);
}

.titre-google {
  color: #8bb026;
  text-align: center;
  margin-top: 25px;
}


.green-card-container-girl {
  position: absolute;
  bottom: 35vh;
  left: 22vh;
}

.green-card-girl {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20px;
  width: fit-content;
  height: fit-content;
  background-color: #8bb026;
  border-radius: 15px;
  padding-bottom: 35px;
  padding-top: 25px;
}


.cursor-girl {
  position: absolute;
  bottom: 15px;
  left: -5px;
  width: 2vh;
  height: 2vh;
  background-color: #8bb026;
  transform: rotate(-40deg);
}

.note-glo {
  position: absolute;
  right: 15px;
  top: 15vh;
  width: 13vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;


}

.note-glo .score {
  margin-left: 0;
}

.note-glo h3,
.note-glo .score,
.note-glo .score span {
  color: #8bb026;
  text-transform: uppercase;
}

.note-glo img {
  width: 30px;
}

.desc {
  margin-top: 10px;
}

.desc .note {
  font-size: 1.5rem;
}

.note-glo .desc p,
.note-glo .desc span {
  color: #8bb026;
  text-transform: uppercase;
  text-align: center;
  font-weight: 700;
}


.botanique-logo img {
  position: absolute;
  bottom: 10px;
  right: 15px;
  width: 10vw;
  z-index:1000
}

.conteneur.saviez-vous{
  margin-top: 1em;
}
/* Reset et styles de base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Dosis', sans-serif;
  font-size: 14px;
}

body {
  background-color: #f5f5f5;
  color: #333;
}

h2 {
  font-size: 1.2rem;
  text-transform: uppercase;
  color: #00564B;
}

h3 {
  font-size: 1rem;
  color: #666;
}

/* Container principal */
.dashboard-container {
  max-width: 1800px;
  margin: 0 auto;
  padding: 20px;
  background-color: white;
  min-height: 100vh;
}

/* Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left, .header-right {
  width: 33%;
}

.header-center {
  width: 33%;
  text-align: center;
}

.dashboard-title {
  color: #00564B;
  font-size: 1.8rem;
  font-weight: bold;
}

.period {
  color: #333;
  font-size: 1rem;
  text-align: center;
  line-height: 1.4;
}

/* Layout principal */
.dashboard-content {
  display: flex;
  gap: 20px;
}

.content-left {
  width: 65%;
}

.content-right {
  width: 35%;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
}

/* Cartes principales */
.main-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.main-card {
  width: 48%;
  border: 1px solid #8bb026;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.card-header {
  background-color: #00564B;
  color: white;
  display: flex;
  justify-content: space-between;
  padding: 10px 15px;
}

.card-icon {
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-icon img {
  width: 40px;
  height: 40px;
}

.card-title {
  font-size: 0.9rem;
  text-transform: uppercase;
  color: white;
}

.card-title span {
  font-weight: bold;
  color: white;
  font-size: 1rem;
}

.card-respondents {
  text-align: right;
}

.respondents-count {
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
}

.respondents-label {
  font-size: 0.8rem;
  color: white;
}

.card-content {
  padding: 15px;
  background-color: #f0f8f0;
  min-height: 150px;
  display: flex;
  flex-direction: column;
}

.graph-container {
  flex: 1;
  min-height: 100px;
  background-color: white;
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.score-display {
  text-align: center;
  margin-top: 10px;
}

.score {
  font-size: 2rem;
  font-weight: bold;
  color: #8bb026;
}

.score span {
  font-size: 1.2rem;
  color: #666;
}



/* Sections */
.section {
  margin-bottom: 20px;
}

.section-title {
  color: #00564B;
  font-size: 1.2rem;
  margin-bottom: 15px;
  font-weight: bold;
  text-transform: uppercase;
}

.section-subtitle {
  color: #666;
  font-size: 1rem;
  margin-bottom: 10px;
}

/* Cartes de marché */
.market-cards, .delivery-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.market-card {
  width: calc(25% - 12px);
  display: flex;
  border: 1px solid #8bb026;
  border-radius: 8px;
  overflow: hidden;
  height: 60px;
}

.market-icon {
  width: 60px;
  background-color: #00564B;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
}

.market-icon img {
  width: 30px;
  height: 30px;
}

.market-info {
  flex: 1;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: white;
}

.market-score {
  font-size: 1.5rem;
  font-weight: bold;
  color: #8bb026;
  line-height: 1;
}

.market-respondents {
  font-size: 0.7rem;
  color: #666;
  margin-top: 2px;
}

/* Cartes de livraison */
.delivery-cards .market-card {
  width: calc(50% - 8px);
}

/* Carte Service Client */
.service-client-card {
  border: 1px solid #8bb026;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
  background-color: #f0f8f0;
}

.service-client-card .card-header {
  background-color: #00564B;
  color: white;
  display: flex;
  justify-content: space-between;
  padding: 10px 15px;
}

.service-client-card .card-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  padding: 20px;
}

.service-client-card .score {
  font-size: 2.5rem;
}

/* Section Le Saviez-Vous */
.saviez-vous-cards {
  display: flex;
  gap: 15px;
}

.saviez-vous-card {
  width: calc(50% - 8px);
  display: flex;
  border: 1px solid #8bb026;
  border-radius: 8px;
  overflow: hidden;
  height: 70px;
}

.saviez-vous-icon {
  width: 80px;
  background-color: #00564B;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 10px;
  color: white;
}

.saviez-vous-icon img {
  width: 30px;
  height: 30px;
  margin-bottom: 5px;
}

.saviez-vous-title {
  font-size: 0.7rem;
  text-align: center;
  color: white;
  line-height: 1.2;
}

.saviez-vous-score {
  flex: 1;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: white;
}

.saviez-vous-score .score {
  font-size: 1.5rem;
  line-height: 1;
}

.saviez-vous-score .respondents {
  font-size: 0.7rem;
  color: #666;
  margin-top: 2px;
}


/* 3e colonne */
.thirdCol {
  background-color: rgba(255, 228, 196, 0.212);
  border-radius: 8px;
  position: relative;
  padding-top: 15px;
  margin-top: 0;
}

.couple {
  position: absolute;
  width: 200px;
  max-width: 90%;
  bottom: 10px;
}

.green-card-container-boy {
  position: absolute;
  bottom: 250px;
  left: 15px;
}

.green-card-boy,
.green-card-girl {
  height: fit-content !important;
}

.green-card-boy {
  position: relative;
  display: flex;
  align-items: center;
  padding: 10px;
  width: 150px;
  max-width: 90%;
  height: fit-content;
  background-color: #8bb026;
  border-radius: 8px;
  padding-bottom: 20px;
  padding-top: 15px;
}

.green-card-boy p,
.green-card-girl p {
  color: white;
  font-weight: 700;
  z-index: 500;
}

.green-card-boy .ratings,
.green-card-boy .ratings .score,
.green-card-boy .ratings .score span {
  color: white;
}

.green-card-girl .ratings,
.green-card-girl .ratings .score,
.green-card-girl .ratings .score span {
  color: white;
}

.ratings .score {
  font-size: 1.2rem;
  display: flex;
  align-items: baseline;
}

.ratings .score div {
  margin-left: 15px;
}

.ratings .score img {
  width: 15px;
}

.ratings .score span {
  font-size: 0.8rem;
}

.ratings {
  position: absolute;
  bottom: 5px;
  right: 20px
}


.cursor-boy {
  position: absolute;
  bottom: -5px;
  left: 30px;
  width: 2vh;
  height: 2vh;
  background-color: #8bb026;
  transform: rotate(-40deg);
}

.titre-google {
  color: #8bb026;
  text-align: center;
  margin-top: 25px;
}


.green-card-container-girl {
  position: absolute;
  bottom: 180px;
  left: 100px;
}

.green-card-girl {
  position: relative;
  display: flex;
  align-items: center;
  padding: 10px;
  width: 170px;
  max-width: 90%;
  height: fit-content;
  background-color: #8bb026;
  border-radius: 8px;
  padding-bottom: 20px;
  padding-top: 15px;
}


.cursor-girl {
  position: absolute;
  bottom: 15px;
  left: -5px;
  width: 2vh;
  height: 2vh;
  background-color: #8bb026;
  transform: rotate(-40deg);
}

.note-glo {
  position: absolute;
  right: 10px;
  top: 50px;
  width: 120px;
  max-width: 90%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.note-glo .score {
  margin-left: 0;
  font-size: 1.5rem;
}

.note-glo h3,
.note-glo .score,
.note-glo .score span {
  color: #8bb026;
  text-transform: uppercase;
  font-size: 0.9rem;
}

.note-glo img {
  width: 20px;
}

.desc {
  margin-top: 5px;
}

.desc .note {
  font-size: 1.2rem;
}

.note-glo .desc p,
.note-glo .desc span {
  color: #8bb026;
  text-transform: uppercase;
  text-align: center;
  font-weight: 700;
  font-size: 0.7rem;
}


.botanique-logo img {
  position: absolute;
  bottom: 5px;
  right: 10px;
  width: 80px;
  max-width: 90%;
}

.section-avis-google .botanique-logo img {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 80px;
}

.section-avis-google .couple {
  position: absolute;
  bottom: 10px;
  left: 10px;
  width: 150px;
}

/* Styles pour les widgets Toucan */
.toucan-widget {
  max-width: 100%;
  overflow: hidden;
}

/* Styles pour les scripts Toucan */
script[src*="toucantoco.com"] {
  max-width: 100%;
  display: block;
}

/* Styles pour les conteneurs d'embed */
.embed {
  overflow: hidden;
  max-width: 100%;
}

/* Styles pour les iframes générées par Toucan */
iframe {
  max-width: 100% !important;
  border: none !important;
}

.graph-container iframe {
  width: 100% !important;
  height: 120px !important;
  background-color: white;
}

/* Sections spécifiques */
.section-par-marche,
.section-par-livraison,
.section-saviez-vous {
  width: 100%;
  margin-bottom: 20px;
}

.section-par-marche h2,
.section-par-livraison h2,
.section-saviez-vous h2 {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: #333;
  font-weight: bold;
}

.section-saviez-vous h3 {
  font-size: 1rem;
  margin-bottom: 10px;
  color: #666;
}

.market-cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: space-between;
}

.section-par-livraison {
  width: 100%;
  margin-bottom: 20px;
}

.livraison-cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: space-between;
}

.section-avis-google {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  height: 100%;
  min-height: 400px;
}

.avis-exemples {
  margin-top: 20px;
  width: 90%;
}

.avis-exemples p {
  background-color: #8bb026;
  color: white;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 10px;
  font-size: 0.8rem;
}

.note-globale {
  font-size: 2rem;
  color: #8bb026;
  font-weight: bold;
  margin-bottom: 5px;
}

.etoiles {
  margin-bottom: 10px;
}

.etoiles img {
  width: 20px;
  height: 20px;
}

/* Section Avis Google */
.google-reviews {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.global-rating {
  text-align: center;
  margin-bottom: 20px;
}

.rating-label {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 5px;
  color: #00564B;
}

.rating-score {
  font-size: 2.5rem;
  font-weight: bold;
  color: #8bb026;
}

.rating-stars {
  margin: 10px 0;
}

.rating-stars img {
  width: 25px;
  height: 25px;
}

.rating-count {
  font-size: 0.9rem;
  color: #666;
}

.rating-count span {
  font-weight: bold;
}

.review-examples {
  margin-top: 20px;
}

.review {
  background-color: #8bb026;
  color: white;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
  font-size: 0.9rem;
  line-height: 1.3;
}

.illustration {
  margin-top: auto;
  position: relative;
  height: 200px;
}

.illustration .couple {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 180px;
}

.illustration .logo {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 80px;
}

/* Media Queries pour la réactivité */
@media screen and (max-width: 1400px) {
  .market-card {
    width: calc(50% - 8px);
  }
}

@media screen and (max-width: 1200px) {
  .dashboard-content {
    flex-direction: column;
  }

  .content-left, .content-right {
    width: 100%;
  }

  .main-cards {
    flex-direction: column;
  }

  .main-card {
    width: 100%;
  }

  .market-card, .widget {
    width: 48%;
  }
}

@media screen and (max-width: 768px) {
  .market-card, .delivery-cards .market-card, .saviez-vous-card {
    width: 100%;
  }

  .saviez-vous-cards {
    flex-direction: column;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: center;
  }

  .header-left, .header-center, .header-right {
    width: 100%;
    text-align: center;
    margin-bottom: 10px;
  }

  .widget-container {
    flex-direction: column;
  }

  .score {
    font-size: 1.5rem;
  }
}
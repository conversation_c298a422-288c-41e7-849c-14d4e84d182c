// Script de debug ultra-simple pour les éléments "pasdutout"
// Version qui fonctionne même avec des erreurs réseau

console.log('🔍 DÉBUT DU DEBUG PASDUTOUT');

// Fonction de base qui fonctionne toujours
function debugPasdutout() {
    try {
        const elements = document.querySelectorAll('.pasdutout');
        console.log('📊 RÉSULTATS:');
        console.log('Nombre d\'éléments trouvés:', elements.length);
        
        if (elements.length === 0) {
            console.log('❌ Aucun élément trouvé avec la classe "pasdutout"');
            return [];
        }
        
        // Affichage simple élément par élément
        for (let i = 0; i < elements.length; i++) {
            console.log(`\n--- ÉLÉMENT ${i + 1} ---`);
            
            const el = elements[i];
            
            // Informations de base (toujours accessibles)
            console.log('Tag:', el.tagName);
            console.log('Nom de classe:', el.className);
            
            // ID si présent
            if (el.id) {
                console.log('ID:', el.id);
            } else {
                console.log('ID: (aucun)');
            }
            
            // Tentative d'accès au texte
            let texte = '';
            try {
                texte = el.textContent || el.innerText || '';
                if (texte.trim()) {
                    console.log('Texte:', '"' + texte.trim().substring(0, 100) + '"');
                } else {
                    console.log('Texte: (vide)');
                }
            } catch (e) {
                console.log('Texte: (erreur d\'accès)');
            }
            
            // Attributs principaux
            try {
                if (el.getAttribute('href')) {
                    console.log('Lien href:', el.getAttribute('href').substring(0, 100) + '...');
                }
                if (el.getAttribute('style')) {
                    console.log('Style inline:', el.getAttribute('style').substring(0, 100) + '...');
                }
                if (el.getAttribute('class')) {
                    console.log('Classes complètes:', el.getAttribute('class'));
                }
            } catch (e) {
                console.log('Attributs: (erreur d\'accès)');
            }
            
            // Référence directe à l'élément
            console.log('Élément DOM:', el);
        }
        
        console.log('\n✅ DEBUG TERMINÉ');
        return elements;
        
    } catch (error) {
        console.log('❌ ERREUR GÉNÉRALE:', error.message);
        return [];
    }
}

// Fonction encore plus basique
function basePasdutout() {
    const els = document.querySelectorAll('.pasdutout');
    console.log('Éléments .pasdutout:', els.length);
    
    for (let i = 0; i < els.length; i++) {
        console.log(i + 1 + '.', els[i].tagName, '- Classes:', els[i].className);
    }
    
    return els;
}

// Fonction pour inspecter un élément spécifique
function inspectElement(index) {
    const elements = document.querySelectorAll('.pasdutout');
    
    if (index < 1 || index > elements.length) {
        console.log('❌ Index invalide. Utilisez un nombre entre 1 et', elements.length);
        return null;
    }
    
    const el = elements[index - 1];
    console.log(`🔍 INSPECTION ÉLÉMENT ${index}:`);
    console.log('Élément:', el);
    console.log('Tag:', el.tagName);
    console.log('Classes:', el.className);
    console.log('HTML externe:', el.outerHTML.substring(0, 200) + '...');
    
    return el;
}

// Exécution automatique
console.log('🚀 Exécution automatique...');
debugPasdutout();

console.log('\n💡 Fonctions disponibles:');
console.log('- debugPasdutout() : Debug complet');
console.log('- basePasdutout() : Version ultra-simple');
console.log('- inspectElement(1) : Inspecter l\'élément 1, 2, 3 ou 4');

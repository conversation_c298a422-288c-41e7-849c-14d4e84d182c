// Script ultra-simple pour vérifier les éléments avec la classe "pasdutout"
// Version sans erreurs CORS - Copiez-collez dans la console du navigateur

// Fonction ultra-simple sans erreurs
function simpleCheckPasdutout() {
    const elements = document.querySelectorAll('.pasdutout');
    console.log('=== ÉLÉMENTS PASDUTOUT ===');
    console.log('Nombre:', elements.length);
    
    for (let i = 0; i < elements.length; i++) {
        const el = elements[i];
        console.log(`\n${i + 1}. Tag: ${el.tagName}`);
        console.log(`   Classes: ${el.className}`);
        console.log(`   ID: ${el.id || 'Aucun'}`);
        
        // Contenu texte sécurisé
        try {
            const text = el.textContent || el.innerText || '';
            console.log(`   Texte: "${text.trim().substring(0, 80)}${text.length > 80 ? '...' : ''}"`);
        } catch (e) {
            console.log(`   Texte: Non accessible`);
        }
        
        // Attributs
        try {
            const attrs = [];
            for (let attr of el.attributes) {
                attrs.push(`${attr.name}="${attr.value}"`);
            }
            console.log(`   Attributs: ${attrs.join(', ')}`);
        } catch (e) {
            console.log(`   Attributs: Non accessibles`);
        }
        
        // Parent element info
        try {
            const parent = el.parentElement;
            if (parent) {
                console.log(`   Parent: ${parent.tagName}${parent.className ? '.' + parent.className.split(' ').join('.') : ''}`);
            }
        } catch (e) {
            console.log(`   Parent: Non accessible`);
        }
    }
    
    console.log('\n=== FIN ===');
    return elements;
}

// Version encore plus simple - juste les bases
function ultraSimpleCheck() {
    const elements = document.querySelectorAll('.pasdutout');
    console.log(`Trouvé ${elements.length} éléments avec classe "pasdutout"`);
    
    elements.forEach((el, i) => {
        console.log(`${i + 1}. ${el.tagName} - Classes: "${el.className}"`);
    });
    
    return elements;
}

// Version avec highlighting visuel simple
function highlightPasdutout() {
    const elements = document.querySelectorAll('.pasdutout');
    
    elements.forEach((element, index) => {
        element.style.border = '3px solid red';
        element.style.backgroundColor = 'yellow';
        
        // Ajouter un numéro
        const label = document.createElement('span');
        label.textContent = ` [PASDUTOUT-${index + 1}] `;
        label.style.backgroundColor = 'red';
        label.style.color = 'white';
        label.style.padding = '2px';
        label.style.fontWeight = 'bold';
        label.className = 'pasdutout-marker';
        
        element.insertBefore(label, element.firstChild);
    });
    
    console.log(`✨ ${elements.length} éléments "pasdutout" mis en surbrillance`);
    return elements;
}

// Fonction pour nettoyer la surbrillance
function clearHighlight() {
    const elements = document.querySelectorAll('.pasdutout');
    elements.forEach(element => {
        element.style.border = '';
        element.style.backgroundColor = '';
        const markers = element.querySelectorAll('.pasdutout-marker');
        markers.forEach(marker => marker.remove());
    });
    console.log('🧹 Surbrillance nettoyée');
}

// Auto-exécution et instructions
console.log('📋 Script simple pour éléments "pasdutout" chargé!');
console.log('💡 Fonctions disponibles:');
console.log('   - simpleCheckPasdutout() : Analyse détaillée');
console.log('   - ultraSimpleCheck() : Analyse basique');
console.log('   - highlightPasdutout() : Surbrillance visuelle');
console.log('   - clearHighlight() : Nettoyer la surbrillance');
console.log('');
console.log('🚀 Exécution automatique de ultraSimpleCheck():');
ultraSimpleCheck();

<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard de Satisfaction</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Dosis:wght@200..800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="toucan.css">

</head>

<body>
    <div class="primaryBox">
        <!-- Header avec séparateur -->
        

        <!-- Grille principale -->
        <div class="grillePrincipale">
            <!-- première colonne -->
            <div class="col">
                <!-- 1ere ligne -->
                <h1 class="grand-titre ">LEUR SATISFACTION...</h1>
                <div class="card">
                    <div class="entete">
                        <div class="iconCo">
                            <img src="./images/ico-magasin.png" alt="">
                            <p>Lors d'un achat <br><span>en magasin</span></p>
                        </div>
                        <div class="repondant">
                            <span>répondants</span>
                            <div style="width:100px;height:100px">

                                <script async src="https://satisfactory.toucantoco.com/scripts/embedLauncher.js?id= d15245ba-dabe-9243-777c-6a0b28263361&token=_0XBPWQQ_a242608a-12c0-4d7d-af8f-9b305fea9524" type="text/javascript"></script>
                            </div>
                        </div>
                    </div>
					
										
					
                    <div class="embed">

					<script async src="https://satisfactory.toucantoco.com/scripts/embedLauncher.js?id=873f13db-532d-4a95-b846-40b14f6af2fa&token=_0XBPWQQ_a242608a-12c0-4d7d-af8f-9b305fea9524" type="text/javascript"></script>
					
					</div>
                    <div class="score">
                        
						
						
						
						<script async src="https://satisfactory.toucantoco.com/scripts/embedLauncher.js?id=c9b966b7-737b-488f-b0f5-4471d3e42a81&token=_0XBPWQQ_a242608a-12c0-4d7d-af8f-9b305fea9524" type="text/javascript"></script>
                    </div>
                </div>

                <!-- 2e ligne -->
                <div class="conteneur">
                    <h3>Et par marché ça donne quoi?</h3>
                    <div class="widget-container">
                        
                        <div class="widget">
                            <div class="w-icon">
                                <img src="./images/ico-jardin.png" alt="">
                                <p>JARDIN</p>
                            </div>
<script async src="https://satisfactory.toucantoco.com/scripts/embedLauncher.js?id=d1936807-11d4-4d13-a5d7-21812b2d563a&token=_0XBPWQQ_a242608a-12c0-4d7d-af8f-9b305fea9524" type="text/javascript"></script>

                        </div>
                        <div class="widget">
                            <div class="w-icon">
                                <img src="./images/ico-animalerie.png" alt="">
                                <p>ANIMALERIE
                                </p>
                            </div>

<script async src="https://satisfactory.toucantoco.com/scripts/embedLauncher.js?id=efaef356-8d75-47d5-8b61-ff961f06b25d&token=_0XBPWQQ_a242608a-12c0-4d7d-af8f-9b305fea9524" type="text/javascript"></script>

                        </div>
                    </div>
                    <div class="widget-container">
                        
                        <div class="widget">
                            <div class="w-icon">
                                <img src="./images/ico-marchebio.png" alt="">
                                <p>MARCHÉ BIO</p>
                            </div>
<script async src="https://satisfactory.toucantoco.com/scripts/embedLauncher.js?id=636cc5df-5c0b-4599-bc20-5ca826e92bac&token=_0XBPWQQ_a242608a-12c0-4d7d-af8f-9b305fea9524" type="text/javascript"></script>

                        </div>
                        <div class="widget">
                            <div class="w-icon">
                                <img src="./images/ico-maison.png" alt="">
                                <p>MAISON</p>
                            </div>

<script async src="https://satisfactory.toucantoco.com/scripts/embedLauncher.js?id=8ca843d6-a5e3-4ac2-badf-05e0fba7deed&token=_0XBPWQQ_a242608a-12c0-4d7d-af8f-9b305fea9524" type="text/javascript"></script>

                        </div>
                    </div>
                </div>

                <!-- 3e ligne -->
                <div class="conteneur">
                    <h2>LE SAVIEZ-VOUS</h3>
                    <h3>Notes attribué par nos clients pour ...</h3>
                    <div class="widget-container">
                        
                        <div class="widget">
                            <div class="w-icon">
                                <img src="./images/ico-prix.png" alt="">
                                <p style="font-size: small;">LE RAPPORT
                                    QUALITÉ / PRIX</p>
                            </div>
                            <div class="score-container">
<script async src="https://satisfactory.toucantoco.com/scripts/embedLauncher.js?id=39607b4c-2127-455e-922d-bd033e7d18f2&token=_0XBPWQQ_a242608a-12c0-4d7d-af8f-9b305fea9524" type="text/javascript"></script>
                            </div>

                        </div>
                        <div class="widget">
                            <div class="w-icon">
                                <img src="./images/ico-animalerie.png" alt="">
                                <p style="font-size: small;">DISPONIBILITÉ POUR NOS CLIENTS</p>
                            </div>

                            <div class="score-container">
<script async src="https://satisfactory.toucantoco.com/scripts/embedLauncher.js?id=cccc9a61-71f5-4ad9-b0e8-e155cf37983e&token=_0XBPWQQ_a242608a-12c0-4d7d-af8f-9b305fea9524" type="text/javascript"></script>
                            </div>

                        </div>
                    </div>
                    
                </div>
            </div>
            <!-- deuxième colonne -->
            <div class="col">
                <!-- 1er ligne -->
                <div class="petit-titre">Pour la période <br>  <span class="titre-moyen font-bold" id="periode-dates">
                </span>
        </div>

                <div class="card">
                    <div class="entete">
                        <div class="iconCo">
                            <img style="width: 50px; height: 50px;" src="./images/ico-internet.png" alt="">
                            <p>Lors d'un achat <br><span>SUR BOTANIC.COM</span></p>
                        </div>
						
						<div class="repondant">
                            
                            <div>
                                <span>répondants</span>
                                <div >
                            <script async src="https://satisfactory.toucantoco.com/scripts/embedLauncher.js?id=23656381-bb5e-4580-8201-230c9489c136&token=_0XBPWQQ_a242608a-12c0-4d7d-af8f-9b305fea9524" type="text/javascript"></script>
</div>
                        </div>
                    
                    </div>
					</div> 
						 
					<div class="embed">
						
               
					
					<script async src="https://satisfactory.toucantoco.com/scripts/embedLauncher.js?id=a1f4923e-5827-4b4b-a735-a2f2c44dcedd&token=_0XBPWQQ_a242608a-12c0-4d7d-af8f-9b305fea9524" type="text/javascript"></script>
					
					</div>
                    <div class="score">
                       <script async src="https://satisfactory.toucantoco.com/scripts/embedLauncher.js?id=66fc0b66-d25e-47f2-9efe-129d34d6e19d&token=_0XBPWQQ_a242608a-12c0-4d7d-af8f-9b305fea9524" type="text/javascript"></script>
                    </div>
                </div>

                <!-- 2e ligne -->
                <div class="conteneur">
                    <h3>Et par mode de livraison ?</h3>
                    <div class="widget-container">
                        
                        <div class="widget">
                            <div class="w-icon">
                                <img src="./images/ico-clickcollect.png" alt="">
                                <p>Click & collect</p>
                            </div>
                            <div class="score-container">
                                <div class="w-score">
                                    9.3<span>/10</span>
                                </div>
                                <div class="w-repondant">392<span>répondants</span></div>
                            </div>

                        </div>
                        <div class="widget">
                            <div class="w-icon">
                                <img src="./images/ico-livraison.png" alt="">
                                <p>Livraison à domicile</p>
                            </div>

                            <div class="score-container">
                                <div class="w-score">
                                    9.3<span>/10</span>
                                </div>
                                <div class="w-repondant">392<span>répondants</span></div>
                            </div>

                        </div>
                    </div>
                   
                </div>

                <!-- 3e ligne -->
                <div class="card">
                    <div class="entete">
                        <div class="iconCo">
                            <img src="./images/ico-service.png" alt="">
                            <p>Lors d'un contact avec <br><span>le service client</span></p>
                        </div>
                        <div class="repondant">392 <span>répondants</span></div>
                    </div>
                    <div class="embed service_client">
                        <script async src="https://satisfactory.toucantoco.com/scripts/embedLauncher.js?id=873f13db-532d-4a95-b846-40b14f6af2fa&token=_0XBPWQQ_a242608a-12c0-4d7d-af8f-9b305fea9524" type="text/javascript"></script>
					
                    </div>
                    <div class="score" style="position:relative;left:10px;bottom:10px">
                        9.3<span>/10</span>
                    </div>
                </div>
            </div>


            <!-- troisième colonne -->
            <div class="col thirdCol">
                <h1 class="grand-titre titre-google ">LEURS AVIS GOOGLE</h1>
                <div class="note-glo">
                    <h3>Note gloable</h3>
                    <div class="score score-google">
                        3<span>/5</span>
                    </div>
                    <div>
                        <img src="./images/ico-etoile-4.png" alt="">
                        <img src="./images/ico-etoile-4.png" alt="">
                        <img src="./images/ico-etoile-4.png" alt="">
                        <img src="./images/ico-etoile-0.png" alt="">
                        <img src="./images/ico-etoile-0.png" alt="">
                    </div>
                    <div class="desc">
                        <p>
                            Pour <span class="note">188</span> avis

                        </p>
                        <p>Sur la période</p>
                    </div>

                </div>

                <div class="botanique-logo">
                    <img src="./images/botanic-logo.jpg" alt="">
                </div>
                <img class="couple" src="./images/couple_courses.png" alt="">
                <div class="green-card-container-boy">
                    <div class="green-card-boy">
                        <p>Lorem ipsum, dolor sit amet consectetur adipisicing elit. Natus assumenda, quibusdam rem dolore nesciunt ut consequatur enim unde ipsam molestias voluptatibus pariatur? Praesentium odit illo delectus harum optio distinctio odio.</p>
                        <div class="ratings">
                            <div class="score">
                                9.3<span>/10</span> <span class="start">
                                    <div>
                                        <img src="./images/ico-etoile-4.png" alt="">
                                        <img src="./images/ico-etoile-4.png" alt="">
                                        <img src="./images/ico-etoile-4.png" alt="">
                                        <img src="./images/ico-etoile-0.png" alt="">
                                    </div>
                                    
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="cursor-boy"></div>
                </div>

                <div class="green-card-container-girl">
                    <div class="green-card-girl">
                        <p>Lorem ipsum, dolor sit amet consectetur adipisicing elit. Perferen autem eveniet minus quos provident.</p>
                        <div class="ratings">
                            <div class="score">
                                9.3<span>/10</span> <span class="start">
                                    <div>
                                        <img src="./images/ico-etoile-4.png" alt="">
                                        <img src="./images/ico-etoile-4.png" alt="">
                                        <img src="./images/ico-etoile-4.png" alt="">
                                        <img src="./images/ico-etoile-0.png" alt="">
                                    </div>
                                    
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="cursor-girl"></div>
                </div>
            </div>
        </div>

    </div>
    </div>
    

    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM chargé, exécution du script de date");
            
            const now = new Date();
            const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
            const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            
            console.log("Dates calculées:", firstDay, lastDay);
            
            const formatDate = date => {
                return ('0' + date.getDate()).slice(-2) + '/' + 
                       ('0' + (date.getMonth() + 1)).slice(-2) + '/' + 
                       date.getFullYear();
            };
            
            const formattedFirstDay = formatDate(firstDay);
            const formattedLastDay = formatDate(lastDay);
            console.log("Dates formatées:", formattedFirstDay, formattedLastDay);
            
            const periodeElement = document.getElementById('periode-dates');
            console.log("Élément période trouvé:", periodeElement);
            
            if (periodeElement) {
                periodeElement.textContent = formattedFirstDay + ' → ' + formattedLastDay;
                console.log("Texte défini sur:", periodeElement.textContent);
            } else {
                console.error("L'élément avec l'ID 'periode-dates' n'a pas été trouvé");
                
              
                const allSpans = document.querySelectorAll('span');
                console.log("Nombre total de spans:", allSpans.length);
                
                const possibleElements = Array.from(allSpans).filter(span => 
                    span.className && span.className.includes('titre-moyen'));
                
                console.log("Spans avec classe titre-moyen:", possibleElements.length);
                
                if (possibleElements.length > 0) {
                    possibleElements[0].textContent = formattedFirstDay + ' → ' + formattedLastDay;
                    console.log("Texte défini sur un élément alternatif");
                }
            }
        });
    </script>
</body>

</html>

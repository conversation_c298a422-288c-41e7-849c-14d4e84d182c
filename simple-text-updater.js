// Script simple pour changer les textes
const textConfig = {
    pasdutout: { leftText: "Pas du tout satisfait(e) 😟", rightText: "Très satisfait(e) 😍" },
    nondutout: { leftText: "Non, pas du tout 😟", rightText: "Oui, tout à fait 😍" },
    attention: { leftText: "Pas attentionnées 😟", rightText: "Très attentionnées 😍" }
};

function updateTexts() {
    // Détecter le type de page
    let pageType = 'pasdutout';
    if (document.querySelector('.nondutout')) pageType = 'nondutout';
    if (document.querySelector('.attention')) pageType = 'attention';
    
    const config = textConfig[pageType];
    console.log(`Type: ${pageType}`);
    
    // Modifier tous les textes
    document.querySelectorAll('.left-green-text').forEach(el => {
        el.textContent = config.leftText;
        el.style.backgroundColor = 'yellow';
        setTimeout(() => el.style.backgroundColor = '', 1000);
    });
    
    document.querySelectorAll('.right-red-text').forEach(el => {
        el.textContent = config.rightText;
        el.style.backgroundColor = 'yellow';
        setTimeout(() => el.style.backgroundColor = '', 1000);
    });
    
    console.log('✅ Textes mis à jour');
}

// Exécuter après chargement et en continu
window.addEventListener('load', () => {
    setTimeout(updateTexts, 500);
    setInterval(updateTexts, 1000);
});

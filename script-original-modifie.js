//charger le document
$(document).ready(function() {
    console.log('Document ready');
    
    // Configuration des textes selon le type de page
    const textConfig = {
        pasdutout: {
            leftText: "Pas du tout satisfait(e) 😟",
            rightText: "Très satisfait(e) 😍"
        },
        nondutout: {
            leftText: "Non, pas du tout 😟",
            rightText: "Oui, tout à fait 😍"
        },
        attention: {
            leftText: "Pas attentionnées 😟",
            rightText: "Très attentionnées 😍"
        }
    };
    
    // Fonction pour déterminer le type de page
    function getPageType() {
        // Chercher les éléments avec les classes spécifiques
        if (document.querySelector('.pasdutout')) return 'pasdutout';
        if (document.querySelector('.nondutout')) return 'nondutout';
        if (document.querySelector('.attention')) return 'attention';
        return 'pasdutout'; // Par défaut
    }
    
    //ajouter la bande de titre spécifique à chaque page
    //cibler cet element 'td.sg-cell.sg-cell-1.sg-first-cell.sg-cell-label.sg-cell-left-label'
    let element = $('td.sg-cell.sg-cell-1.sg-first-cell.sg-cell-label.sg-cell-left-label');
    console.log('Nombre d\'éléments trouvés:', element.length);
   
    if(element.length > 0){
        // Déterminer le type de page
        const pageType = getPageType();
        const config = textConfig[pageType];
        
        console.log(`Type de page détecté: ${pageType}`);
        console.log(`Textes à utiliser: "${config.leftText}" / "${config.rightText}"`);
        
        //parcourir les elements trouvés et ajouter un texte dans chacun d'eux
        element.each(function(){
            let $td = $(this);
            let $tr = $td.parent();
            // Vérifier si le header existe déjà juste avant ce tr
            if ($tr.prev('.header-band').length === 0) {
                let div = $('<div class="header-band"></div>');
                // Ajout des sous-divs avec les bons textes
                let greenRateDiv = $('<div class="green-rate"></div>');
                let greenRateText = $(`<p class="left-green-text">${config.leftText}</p>`);
                greenRateDiv.append(greenRateText);

                let redRateDiv = $('<div class="red-rate"></div>');
                let redRateText = $(`<p class="right-red-text">${config.rightText}</p>`);
                redRateDiv.append(redRateText);

                div.append(greenRateDiv, redRateDiv);
                $tr.before(div);
                
                console.log(`✅ Header-band créé avec textes: "${config.leftText}" / "${config.rightText}"`);
            }
        });
    } else {
        console.log('Aucun élément trouvé');
    }
});

// Script de debug pour identifier pourquoi les textes ne changent pas visuellement
// Copiez-collez ce code dans la console du navigateur

function debugTextUpdate() {
    console.log('🔍 === DEBUG MISE À JOUR TEXTES ===');
    
    // Configuration des textes
    const textConfig = {
        pasdutout: {
            leftText: "Pas du tout satisfait(e) 😟",
            rightText: "Très satisfait(e) 😍"
        },
        nondutout: {
            leftText: "Non, pas du tout 😟", 
            rightText: "Oui, tout à fait 😍"
        },
        attention: {
            leftText: "Pas attentionnées 😟",
            rightText: "Très attentionnées 😍"
        }
    };
    
    // Chercher tous les éléments
    const allElements = [
        ...document.querySelectorAll('.pasdutout'),
        ...document.querySelectorAll('.nondutout'), 
        ...document.querySelectorAll('.attention')
    ];
    
    console.log(`📊 Éléments trouvés: ${allElements.length}`);
    
    allElements.forEach((element, index) => {
        console.log(`\n🎯 === ÉLÉMENT ${index + 1} ===`);
        
        // Déterminer le type
        let type = null;
        if (element.classList.contains('pasdutout')) type = 'pasdutout';
        else if (element.classList.contains('nondutout')) type = 'nondutout';
        else if (element.classList.contains('attention')) type = 'attention';
        
        console.log(`Type: ${type}`);
        console.log(`Classes: ${element.className}`);
        
        // Chercher header-band
        const headerBand = element.querySelector('.header-band');
        console.log(`Header-band trouvé: ${headerBand ? 'OUI' : 'NON'}`);
        
        if (!headerBand) {
            // Essayer d'autres sélecteurs possibles
            console.log('🔍 Recherche d\'alternatives pour header-band...');
            const alternatives = [
                element.querySelector('[class*="header"]'),
                element.querySelector('[class*="band"]'),
                element.querySelector('.header'),
                element.querySelector('.band')
            ];
            
            alternatives.forEach((alt, i) => {
                if (alt) {
                    console.log(`   Alternative ${i+1} trouvée:`, alt.className);
                }
            });
            
            // Lister tous les enfants
            console.log('📋 Tous les enfants de cet élément:');
            Array.from(element.children).forEach((child, i) => {
                console.log(`   ${i+1}. ${child.tagName}.${child.className}`);
            });
            
            return;
        }
        
        console.log(`✅ Header-band trouvé: ${headerBand.className}`);
        
        // Chercher les éléments p
        const leftText = headerBand.querySelector('p.left-green-text');
        const rightText = headerBand.querySelector('p.right-red-text');
        
        console.log(`Left-green-text trouvé: ${leftText ? 'OUI' : 'NON'}`);
        console.log(`Right-red-text trouvé: ${rightText ? 'OUI' : 'NON'}`);
        
        if (!leftText || !rightText) {
            console.log('🔍 Recherche d\'alternatives pour les textes...');
            
            // Lister tous les p dans header-band
            const allPs = headerBand.querySelectorAll('p');
            console.log(`📋 Tous les éléments p dans header-band (${allPs.length}):`);
            allPs.forEach((p, i) => {
                console.log(`   ${i+1}. Classes: "${p.className}" - Texte: "${p.textContent.trim()}"`);
            });
            
            // Essayer d'autres sélecteurs
            const leftAlternatives = [
                headerBand.querySelector('[class*="left"]'),
                headerBand.querySelector('[class*="green"]'),
                headerBand.querySelector('p:first-child')
            ];
            
            const rightAlternatives = [
                headerBand.querySelector('[class*="right"]'),
                headerBand.querySelector('[class*="red"]'),
                headerBand.querySelector('p:last-child')
            ];
            
            console.log('🔍 Alternatives pour left-text:');
            leftAlternatives.forEach((alt, i) => {
                if (alt) {
                    console.log(`   ${i+1}. ${alt.tagName}.${alt.className} - "${alt.textContent.trim()}"`);
                }
            });
            
            console.log('🔍 Alternatives pour right-text:');
            rightAlternatives.forEach((alt, i) => {
                if (alt) {
                    console.log(`   ${i+1}. ${alt.tagName}.${alt.className} - "${alt.textContent.trim()}"`);
                }
            });
            
            return;
        }
        
        // Afficher les textes actuels
        console.log(`📝 Texte actuel gauche: "${leftText.textContent}"`);
        console.log(`📝 Texte actuel droite: "${rightText.textContent}"`);
        
        // Vérifier si les éléments sont modifiables
        console.log(`🔒 Left readonly: ${leftText.readOnly || leftText.hasAttribute('readonly')}`);
        console.log(`🔒 Right readonly: ${rightText.readOnly || rightText.hasAttribute('readonly')}`);
        
        // Essayer différentes méthodes de modification
        if (type && textConfig[type]) {
            const config = textConfig[type];
            
            console.log(`🔄 Tentative de mise à jour pour type "${type}"...`);
            
            // Méthode 1: textContent
            try {
                leftText.textContent = config.leftText;
                rightText.textContent = config.rightText;
                console.log('✅ Méthode textContent appliquée');
            } catch (e) {
                console.log('❌ Erreur textContent:', e.message);
            }
            
            // Méthode 2: innerHTML
            try {
                leftText.innerHTML = config.leftText;
                rightText.innerHTML = config.rightText;
                console.log('✅ Méthode innerHTML appliquée');
            } catch (e) {
                console.log('❌ Erreur innerHTML:', e.message);
            }
            
            // Méthode 3: innerText
            try {
                leftText.innerText = config.leftText;
                rightText.innerText = config.rightText;
                console.log('✅ Méthode innerText appliquée');
            } catch (e) {
                console.log('❌ Erreur innerText:', e.message);
            }
            
            // Vérifier si le changement a pris effet
            setTimeout(() => {
                console.log(`🔍 Vérification après modification:`);
                console.log(`   Gauche: "${leftText.textContent}"`);
                console.log(`   Droite: "${rightText.textContent}"`);
                
                // Forcer un refresh visuel
                leftText.style.display = 'none';
                rightText.style.display = 'none';
                setTimeout(() => {
                    leftText.style.display = '';
                    rightText.style.display = '';
                    console.log('🔄 Refresh visuel forcé');
                }, 10);
            }, 100);
            
        } else {
            console.log('❌ Type non reconnu ou configuration manquante');
        }
        
        // Ajouter une bordure pour identifier visuellement l'élément
        element.style.border = '2px solid blue';
        headerBand.style.border = '1px solid green';
        if (leftText) leftText.style.border = '1px solid orange';
        if (rightText) rightText.style.border = '1px solid purple';
    });
    
    console.log('\n✅ === DEBUG TERMINÉ ===');
}

// Fonction pour nettoyer les bordures de debug
function clearDebugBorders() {
    const allElements = [
        ...document.querySelectorAll('.pasdutout'),
        ...document.querySelectorAll('.nondutout'), 
        ...document.querySelectorAll('.attention')
    ];
    
    allElements.forEach(element => {
        element.style.border = '';
        const headerBand = element.querySelector('.header-band');
        if (headerBand) {
            headerBand.style.border = '';
            const leftText = headerBand.querySelector('p.left-green-text');
            const rightText = headerBand.querySelector('p.right-red-text');
            if (leftText) leftText.style.border = '';
            if (rightText) rightText.style.border = '';
        }
    });
    
    console.log('🧹 Bordures de debug supprimées');
}

// Exécution automatique
console.log('🚀 Script de debug chargé!');
console.log('💡 Fonctions disponibles:');
console.log('   - debugTextUpdate() : Debug complet');
console.log('   - clearDebugBorders() : Supprimer les bordures de debug');
console.log('');

// Exécuter avec window.onload
if (document.readyState === 'complete') {
    debugTextUpdate();
} else {
    window.addEventListener('load', debugTextUpdate);
}
